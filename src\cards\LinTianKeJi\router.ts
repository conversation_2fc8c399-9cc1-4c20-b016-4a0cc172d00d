import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'linTianKeJiHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg',
      title: '霖天科技AI名片'
    }
  },
  {
    path: '/product-intro',
    name: 'linTianKeJiProductIntro',
    component: () => import('./views/ProductIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo.png',
      title: '霖天科技AI名片 - 产品介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'linTianKeJiAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo.png',
      title: '霖天科技AI名片 - AI宣传员'
    }
  },
  {
    path: '/service-cases',
    name: 'linTianKeJiServiceCases',
    component: () => import('./views/ServiceCasesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo.png',
      title: '霖天科技AI名片 - 服务案例'
    }
  },
  {
    path: '/case-center',
    name: 'linTianKeJiCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo.png',
      title: '霖天科技AI名片 - 案例中心'
    }
  }
]

export default routes
