<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>协会动态</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
     

      <!-- 一码通上饶 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Cellphone /></el-icon>
            协会最新动态
          </h2>

        
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'
import {
  Clock,
  Calendar,
  Grid,
  Money,
  Document,
  Trophy,
  User,
  Reading,
  Management,
  Cellphone,
  ShoppingCart,
  CreditCard,
  Location,
  Microphone,
  Star,
  Connection
} from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/GesiXiehui')
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 80px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 5rem;
  padding-bottom: 6rem;
  padding-left: 1rem;
  padding-right: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 1.5rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.card h2 {
  color: #c41b21;
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #c41b21, #e53e3e);
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -0.375rem;
  top: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  background: #c41b21;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-item.highlight::before {
  background: #e53e3e;
  box-shadow: 0 0 0 4px rgba(196, 27, 33, 0.2);
}

.timeline-date {
  color: #c41b21;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.timeline-content {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* 活动块样式 */
.activity-block {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.activity-block:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.activity-block h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 图片网格和单个图片样式 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.image-item {
  text-align: center;
}

.image-item img {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.image-item img:hover {
  transform: scale(1.02);
}

.image-item p {
  margin: 0.75rem 0 0 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 培训信息样式 */
.training-details {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #c41b21;
  margin-bottom: 1rem;
}

.training-details p {
  margin: 0 0 0.5rem 0;
  color: #555;
  font-size: 0.9rem;
}

.training-details p:last-child {
  margin-bottom: 0;
}

.training-details strong {
  color: #c41b21;
}

/* 服务中心网格样式 */
.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.service-item {
  background: #fafafa;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.service-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  background: white;
}

.service-item img {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.service-item h3 {
  color: #333;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.service-item h3 .el-icon {
  color: #c41b21;
}

/* 一码通上饶样式 */
.yima-block {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.yima-block:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.yima-block h3 {
  color: #333;
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  background: #f8f9fa;
  border-left: 4px solid #c41b21;
  border-radius: 0 0.25rem 0.25rem 0;
  display: inline-block;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.yima-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.yima-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.75rem;
  border-left: 4px solid #c41b21;
  transition: all 0.3s ease;
}

.yima-item:hover {
  background: white;
  box-shadow: 0 4px 12px rgba(196, 27, 33, 0.1);
  transform: translateY(-2px);
}

.yima-icon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.yima-text h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.yima-text p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 协会最新动态样式 */
.news-content {
  margin-top: 1rem;
}

.news-content p {
  margin: 1rem 0;
  color: #555;
  font-size: 0.95rem;
  line-height: 1.8;
  text-align: justify;
  text-indent: 2em; /* 添加首行缩进 */
}

.news-image {
  margin: 2rem auto;
  text-align: center;
  max-width: 90%;
}

.news-image img {
  max-width: 100%;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: block; /* 使图片居中对齐 */
  margin: 0 auto;
}

.goals-list {
  margin: 1.5rem auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  max-width: 95%;
}

.goal-item {
  background: #f8f9fa;
  border-radius: 0.75rem;
  padding: 1.2rem 1.5rem;
  border-left: 4px solid #c41b21;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.goal-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(196, 27, 33, 0.12);
}

.goal-item h4 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  display: flex;
  align-items: center;
}

.goal-item h4:before {
  content: '•';
  margin-right: 0.5rem;
  font-size: 1.4rem;
  color: #c41b21;
}

.goal-item p {
  margin: 0;
  color: #555;
  font-size: 0.95rem;
}

.sub-heading {
  color: #c41b21;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
  border-left: 4px solid #c41b21;
  padding: 0.3rem 0.75rem;
  background: rgba(196, 27, 33, 0.05);
  border-radius: 0 0.25rem 0.25rem 0;
  display: inline-block;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .timeline {
    padding-left: 3rem;
  }

  .timeline::before {
    left: 1rem;
  }

  .timeline-item::before {
    left: 0.125rem;
  }

  .image-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .yima-content {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 新增响应式样式 */
  .goals-list {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .news-image img {
    max-width: 80%;
  }
}

@media (min-width: 1024px) {
  .image-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .service-item img {
    height: auto;
  }

  .activity-block h3 {
    font-size: 1.2rem;
  }

  .yima-content {
    grid-template-columns: repeat(3, 1fr);
  }

  .yima-block h3 {
    font-size: 1.2rem;
  }
}
</style>
