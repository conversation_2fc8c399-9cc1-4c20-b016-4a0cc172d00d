<script setup lang="ts">
import { ref } from 'vue'
import DigitalHuman from '../components/DigitalHuman.vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

const slogan = ref('您好！我是江西万网科技的AI宣传员万小网！')
const pcBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/SZR-PC.jpeg'
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <div class="digital-human-wrapper">
        <DigitalHuman />
      </div>
      <div class="pc-background" :style="{ backgroundImage: `url(${pcBackgroundImage})` }"></div>
    </div>
    
    <div class="bottom-section">
      <div class="content-wrapper">
        <div class="slogan-container">
          <div class="slogan-wrapper">
            <h1 class="slogan">{{ slogan }}</h1>
            <div class="tech-line"></div>
          </div>
        </div>
        
        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.digital-human-wrapper {
  width: 100%;
  height: 100%;
}

.pc-background {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(22, 147, 210, 0.1) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  padding-bottom: 4rem;
  height: 45%;
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

.slogan-wrapper {
  position: relative;
  max-width: 340px;
  padding-bottom: 0.5rem;
}

.tech-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #1693d2 30%, #1693d2 70%, transparent);
}

.slogan {
  font-size: 1.25rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.navigation-container {
  padding: 1rem 1rem 1.5rem 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    height: 50%;
    background: linear-gradient(to bottom, transparent, rgba(22, 147, 210, 0.1) 15%, rgba(255, 255, 255, 0.95));
  }
  
  .content-wrapper {
    justify-content: center;
    padding-bottom: 2rem;
    padding-top: 1rem;
  }
  
  .digital-human-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  
  .digital-human-wrapper {
    width: auto;
    height: 100%;
    min-height: 100vh;
    max-width: none;
    display: flex;
    justify-content: center;
  }
  
  .digital-human-wrapper :deep(img),
  .digital-human-wrapper :deep(video) {
    height: 100%;
    min-height: 100vh;
    width: auto;
    object-fit: cover;
    object-position: center;
  }
  
  .pc-background {
    display: block;
  }
  
  .slogan-wrapper {
    max-width: 700px;
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.75rem;
    background: linear-gradient(135deg, #1693d2, #60a5fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .slogan-container {
    padding: 0 1.5rem 0.5rem 1.5rem;
    margin-bottom: 1rem;
  }
  
  .navigation-container {
    padding: 0 1.5rem 0 1.5rem;
  }
}

@media (min-width: 1200px) {
  .bottom-section {
    height: 55%;
  }
  
  .content-wrapper {
    padding-bottom: 3rem;
    padding-top: 2rem;
  }
  
  .slogan-container {
    margin-bottom: 1.5rem;
  }
  
  .slogan {
    font-size: 2rem;
  }
  
  .navigation-container .navigation-grid {
    max-width: 800px;
  }
  
  .slogan-wrapper {
    max-width: 800px;
  }
}
</style>
