<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import {
  ArrowRight,
  Setting,
  Close
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 类型定义
interface CaseData {
  id: number
  title: string
  image: string
  summary: string
  background?: string
  solution?: string
  value?: string
}

const router = useRouter()

const goBack = () => {
  router.push('/card/WanWangKeJi/case-center')
}

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 弹窗状态
const dialogVisible = ref(false)
const currentCase = ref<CaseData | null>(null)

// 显示案例详情
const showCaseDetail = (caseData: CaseData) => {
  currentCase.value = caseData
  dialogVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
  currentCase.value = null
}

// 智慧生活案例数据
const smartLifeCases = reactive<CaseData[]>([
  {
    id: 1,
    title: '上饶青年卡',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ShangRaoQingNianKa.png',
    summary: '为青年人才提供便捷的住宿预订和综合服务的数字化平台。',
    background: '为吸引和留住青年人才，上饶市推出了青年人才驿站，但线下申请流程繁琐，房源信息不透明。为给青年人才提供更便捷、更优质的服务，亟需一个数字化的线上预约和管理平台。',
    solution: '"上饶青年卡"小程序平台，以"凝聚人才、服务企业"为宗旨，提供了一站式解决方案：\n全流程线上化：实现从线上浏览房源、预约房间，到酒店审批、前台核实、拎包入住的全流程数字化管理。\nPMS系统对接：与酒店管理系统（PMS）无缝对接，实时同步房型、房价、订单等数据，确保信息准确无误。\n灵活定价策略：后台支持根据周末、旺季、节假日等不同时段，灵活调整房间价格，满足精细化运营需求。',
    value: '平台上线后，有效整合了上百套优惠房源，极大便利了来饶求职、创业的青年人才。它不仅是一个住宿预订工具，更是一个集成了就业指导、政策推送、团青活动等功能的综合服务平台，成为服务青年、助力发展的重要载体。'
  },
  {
    id: 2,
    title: '场馆选座票务预约系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/PiaoWuYuYue.png',
    summary: '为体育比赛、文艺演出等活动提供全流程在线化票务解决方案。',
    background: '传统票务销售模式存在诸多痛点：观众需前往售票点排队购票，耗时耗力；主办方人工成本高，且易出现假票、黄牛票等问题；财务对账流程繁琐，容易出错。',
    solution: '江西万网开发的"场馆选座票务预约系统"，是一个专门为体育比赛或各类演出活动提供在线售票服务的应用程序：\n便捷的购票体验：用户可随时随地通过小程序浏览赛事/演出信息，在线选择场次和座位，并通过微信、支付宝等多种方式完成支付，全程无需排队。\n信息透明化：系统实时更新剩余票数和座位信息，让用户能够第一时间掌握购票情况。\n高效的票务管理：主办方可通过后台轻松管理票务信息，设置不同票价，并实时查看销售数据。\n安全的核销入场：用户购票后生成电子二维码，现场通过扫码设备快速核销入场，杜绝了假票问题，并大大提升了入场效率。',
    value: '该系统通过数字化的方式，极大地简化了传统票务的销售与入场流程，为观众和主办方双方都带来了极大的便利。它不仅提升了出票效率和支付效率，更通过无纸化流程，助力环保和二次消费的提升，是一种高效且经济的现代票务解决方案。'
  },
  {
    id: 3,
    title: '新冠疫苗预约接种平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YiQingFangKong.png',
    summary: '为市民提供便捷、高效的新冠疫苗在线预约与信息查询服务。',
    background: '在大规模疫苗接种工作中，线下排队容易造成人群聚集，增加感染风险，且接种效率不高。市民也难以获取准确的疫苗库存和接种点信息。',
    solution: '这是一个便捷、高效的在线疫苗服务系统，旨在解决疫苗接种过程中的信息不对称和排队问题：\n市民端（小程序）：市民可在线完成个人信息登记，查看附近接种点的地址、服务时间、疫苗库存等信息，并根据自己的时间安排，选择合适的时段进行预约。预约成功后会生成预约码。\n管理端（PC后台）：信息全掌握：市、区、县三级卫健委可通过后台全面掌握辖区内接种数据，对预接种人员进行统一安排和管理。站点有效管理：管理员可方便地创建和管理各接种站点的信息，发布公告。智能短信提醒：用户预约成功后，系统会自动发送提醒短信，并可在接种前一天再次提醒，有效避免用户错过接种时间。',
    value: '平台通过"分时段预约"模式，有效分流了接种人群，避免了现场拥堵，大大提高了疫苗接种效率和安全性。它为公众提供了丰富的疫苗信息和健康咨询，为政府部门提供了强大的数据管理和调度能力，是科技抗疫的成功实践。'
  },
  {
    id: 4,
    title: '全域地图智慧旅游',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/QingYuLvYou.png',
    summary: '为游客提供智能化的自助导览服务，提升景区游玩体验。',
    background: '传统旅游模式中，游客常常面临路线规划难、景点信息获取滞后、容易迷路等问题。景区也存在客流引导不畅、景点拥堵等管理难题。',
    solution: '智慧导览系统是通过电子导览设备或小程序，为游客提供智能化、自助化服务的网络控制系统：\n自定义/手绘地图：根据景区特色，绘制精美的手绘地图，并在地图上标注景点、停车场、厕所、商店、餐饮等关键地点信息，一目了然。\n个性化路线推荐：系统可根据游客的兴趣和时间，推荐如"古村文化游"、"山水观光游"、"研学拓展游"等不同主题的个性化游览路线。\n沉浸式景点展示：通过高清图片、短视频、VR全景等多种方式，生动展示核心景点的历史背景、文化内涵和特色景观，让游客未到先知，身临其境。\n语音导览与讲解：当游客到达某个景点时，系统可自动触发语音讲解，提供生动的导览服务。',
    value: '该系统极大地提升了游客的游玩体验，解决了传统旅游中的诸多不便。对景区而言，它实现了智慧化的客流引导，有效防止了拥堵，提升了管理效率和品牌形象，是"旅游+科技"深度融合的产物。'
  },
  {
    id: 5,
    title: '答题小程序',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/DaTiXiaoChengXu.png',
    summary: '提供一个可随时随地进行学习、竞赛、考核的线上答题平台。',
    background: '传统知识竞赛和学习考核活动组织流程复杂、成本高，且受时间地点限制。需要一个轻量化、易于传播的线上平台，来满足各类学习和答题需求。',
    solution: '答题小程序是一个灵活、易用的线上学习教育平台：\n灵活的主题切换：系统前端支持根据不同活动主题（如党建知识、科普知识、安全生产规范等）一键切换界面风格和题库内容。\n强大的考试功能：支持题库随机抽题、考试时间限制、练习模式、成绩实时排名、系统自动阅卷等核心功能。\n便捷的创建与分享：管理员通过后台"三步"（导入考卷、设置规则、发布链接）即可轻松创建一场考试，并通过微信快速分享给考生。\n智能的数据分析：后台可实时查看所有考生的考试情况，自动统计正确率、得分分布等，并支持原始答卷一键下载。',
    value: '答题小程序以其便捷、灵活、低成本的优势，成为各类组织开展知识竞赛、业务考核、课后练习、招聘培训等活动的理想工具。它将学习和考核变得简单、有趣，极大地拓宽了知识传播的渠道。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>智慧生活</h1>
    </div>

    <div class="content">
      <div class="section-description">
        <p>科技便利民生，开启智慧城市生活。</p>
      </div>

      <div class="cards-grid">
        <div
          v-for="case_ in smartLifeCases"
          :key="case_.id"
          class="card-item"
          @click="showCaseDetail(case_)"
        >
          <div class="card-image">
            <img :src="case_.image" :alt="case_.title" />
          </div>
          <div class="card-content">
            <h3>{{ case_.title }}</h3>
            <p>{{ case_.summary }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="dialogVisible" class="dialog-overlay" @click="closeDialog">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h2>{{ currentCase?.title }}</h2>
          <button class="close-btn" @click="closeDialog">
            <el-icon><Close /></el-icon>
          </button>
        </div>
        <div class="dialog-body">
          <div class="case-detail-image">
            <img :src="currentCase?.image" :alt="currentCase?.title" />
          </div>
          <div class="case-detail-content">
            <div class="detail-section">
              <h3>项目背景</h3>
              <p>{{ currentCase?.background }}</p>
            </div>
            <div class="detail-section">
              <h3>解决方案</h3>
              <p v-for="(line, index) in currentCase?.solution?.split('\n')" :key="index">
                {{ line }}
              </p>
            </div>
            <div class="detail-section">
              <h3>平台价值</h3>
              <p>{{ currentCase?.value }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
}

.header {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.section-description {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.section-description p {
  font-size: 1rem;
  color: #666;
  margin: 0;
  font-weight: 500;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.card-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e5e7eb;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #1693d2;
}

.card-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  background: #f8fafc;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-item:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 1.25rem;
}

.card-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.card-content p {
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.dialog-content {
  background: white;
  border-radius: 1rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.dialog-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.dialog-body {
  overflow-y: auto;
  padding: 1.5rem;
}

.case-detail-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  background: #f8fafc;
}

.case-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1693d2;
  margin: 0 0 0.75rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.detail-section p {
  font-size: 0.95rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0 0 0.5rem 0;
}

.detail-section p:last-child {
  margin-bottom: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-image {
    height: 160px;
  }

  .dialog-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .dialog-header {
    padding: 1rem;
  }

  .dialog-body {
    padding: 1rem;
  }

  .case-detail-image {
    height: 150px;
  }
}
</style>
