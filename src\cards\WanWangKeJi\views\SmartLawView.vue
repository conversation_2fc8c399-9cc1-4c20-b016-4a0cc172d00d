<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import {
  ArrowRight,
  Setting,
  Close
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 类型定义
interface CaseData {
  id: number
  title: string
  image: string
  summary: string
  background?: string
  solution?: string
  value?: string
}

const router = useRouter()

const goBack = () => {
  router.push('/card/WanWangKeJi/case-center')
}

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 弹窗状态
const dialogVisible = ref(false)
const currentCase = ref<CaseData | null>(null)

// 显示案例详情
const showCaseDetail = (caseData: CaseData) => {
  currentCase.value = caseData
  dialogVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
  currentCase.value = null
}

// 智慧法治案例数据
const smartLawCases = reactive<CaseData[]>([
  {
    id: 1,
    title: '公安反诈研判系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/FanZhaYanPanXiTong.png',
    summary: '打造全流程反诈作战平台，有效提升预警与打击效率。',
    background: '随着电信网络诈骗犯罪日益猖獗，传统侦查手段在海量信息面前显得力不从心。为有效遏制犯罪势头，亟需一套能够全面、实时、准确分析研判辖区内案事件特点和规律的智能化平台，以实现"预防、发现、打击犯罪"的战略目标。',
    solution: '江西万网基于先进的云服务体系，打造了统一的大数据平台，整合各类内外部关联数据，构建了三大核心智慧应用：\n智慧情报研判：通过多维度数据碰撞和智能分析，挖掘潜在犯罪线索，精准刻画犯罪团伙画像。\n内部公文共享：打破信息壁垒，实现跨部门、跨层级的情报、指令、案卷等信息高效流转与共享。\n智慧求助管理：建立统一的受害人求助入口，规范处置流程，提升群众满意度。',
    value: '本系统是实现反诈工作智能化研判的"利器"，它成功将海量、零散的数据转化为高价值的情报，实现了对数据的智能管控、深度挖掘和高效服务，为信息化作战提供了强有力的技术支撑。'
  },
  {
    id: 2,
    title: '公安反诈举报平台',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/FanZhaJuBao.png',
    summary: '构建警民协作的线上反诈阵地，方便群众快速举报涉诈线索。',
    background: '电信网络诈骗具有隐蔽性强、传播速度快等特点，仅靠公安机关单方面打击难以完全遏制。发动群众、依靠群众，建立一个便捷、高效的线上举报渠道，对于拓宽线索来源、精准预警防范至关重要。',
    solution: '江西万网开发的反诈举报平台，是一个集举报、宣传、管理于一体的综合性系统：\n便捷举报入口：群众可通过小程序随时随地上报涉诈线索，支持文字、图片、录音等多种形式。\n智能化后台：后台系统能自动对举报信息进行分类、归集，并根据区域、案件类型进行大数据分析，为精准打击和防范宣传提供数据支持。\n闭环式反馈：群众提交的举报，系统会自动流转至对应权限的民警处理，处理进度和结果会实时反馈给举报人，形成管理闭环。',
    value: '该平台成功构建了"全民参与、共创平安"的反诈新格局。它不仅是一个线索收集工具，更是连接警民的桥梁，通过精准的数据分析，让反诈宣传更具针对性，有效降低了辖区发案率。'
  },
  {
    id: 3,
    title: '公安警保管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/JinBaoGuanLi.png',
    summary: '实现装备、资产、维修、采购等警保业务全流程信息化管理。',
    background: '传统警务保障工作多依赖纸质记录和线下审批，存在数据查询难、流程耗时长、资产底数不清、资源浪费等问题，难以适应现代化警务工作的快节奏要求。',
    solution: '本系统将分散的装备、资产、维修、采购、车辆、基建等数据统一整合，实现了"六大核心功能"：\n资产/库存管理：建立全局统一的资产电子档案，实时掌握资产状态与库存情况。\n车辆/维修管理：实现车辆使用调度、维修保养线上申请与审批，全过程留痕。\n采购/申领管理：规范物资采购与申领流程，从申请、审批到发放，全程线上化。\n数据统计与分析：自动生成各类统计报表，为预算制定和资源调配提供数据决策支持。',
    value: '系统通过"互联网+现代化管理"，彻底改变了传统警保工作的弊端。它规范了流程、减少了人员跑动、实现了资产动态展现和资源的合理调配，极大提升了警务保障工作的实战化和专业化水平。'
  },
  {
    id: 4,
    title: '公安流动人口管理系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/LiuDongRenKou.png',
    summary: '打通内网数据，实现对流动人口的精准、高效、安全管理。',
    background: '城市流动人口数量庞大、流动性强，传统的人工登记和排查方式存在效率低、信息不准、安全隐患发现不及时等问题。如何准确掌握辖区内流动人口和出租屋情况，是基层警务工作的一大挑战。',
    solution: '本系统通过打通公安内网数据，为基层民警的移动端赋能：\n精准数据比对：实时将民警走访采集的人员信息与内网的图像识别库、犯罪嫌疑人库进行比对，发现风险立即预警。\n移动化办公：民警通过手机即可完成信息采集、风险预警接收、任务处理等工作，极大提升了走访排查效率。\n全方位支持：系统为政府部门全面、准确掌握辖区出租屋和流动人口状况提供了强有力的支持，并为多部门协同管理提供了信息化基础。',
    value: '系统将"汗水警务"升级为"智慧警务"，为基层民警走访排查提供了可靠的数据依据和高效的工具，让安全隐患无处遁形，有力支撑了城市的安全管理。'
  },
  {
    id: 5,
    title: '公安大走访系统',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/GongAnZouFang.png',
    summary: '实现全警大走访工作的信息化采集、分配、管理与统计。',
    background: '"保平安、促和谐"的全警大走访活动，涉及面广、采集信息量大。传统纸质表格填写和手动汇总的方式效率低下，数据难以利用，且流程无法追溯。',
    solution: '本系统将大走访工作全流程线上化、数字化：\n移动信息采集：民警通过手机即可采集走访对象的家庭情况、意见建议、现场照片（含定位）等信息，数据实时上传。\n任务智能分配：系统支持在线分配走访任务，方便警务人员实时查看当日、当月的任务进度，并可导出标准化的走访表格。\n层级化权限管理：不同层级的领导拥有不同范围的数据查看和管理权限，确保信息安全和管理的有序性。\n大数据统计分析：系统能自动对走访数据进行统计分析，直观展示各辖区的工作进展和社情民意。',
    value: '系统为"全警爱民实践大走访"活动提供了有力的技术保障，实现了"底数清、情况明"。它不仅提高了工作效率，更拉近了警民关系，是构建和谐警民环境的重要举措。'
  },
  {
    id: 6,
    title: '公安网格云助手',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/GongAnWangGeYunZhuShou.png',
    summary: '基于"移动互联网+"打造的微网格警民问题解决平台。',
    background: '基层社区的群众求助和问题反馈渠道不畅，传统管理模式响应慢、责任不清。如何快速响应和解决人民群众的"急难愁盼"问题，是提升基层治理能力的关键。',
    solution: '"公安网格云助手"平台通过开放式的组织模式和游戏化的激励机制，重塑了问题解决流程：\n快速上报与统一管理：群众可通过小程序快速上报问题，系统统一接收并流转至对应网格民警。\n创新"任务抢单"机制：引入积分激励和任务抢单模式，激发民警解决问题的主动性和积极性。\n层级化监督：分管、主管领导可在线监督任务处理的全过程，确保问题得到有效解决。',
    value: '平台将传统管理模式升级为"开放式"组织结构，以"互联网+网格民警"的模式，为快速解决群众反馈的问题提供了实用、高效的解决方案，是服务型政府建设的生动实践。'
  },
  {
    id: 7,
    title: '刑警支队数字陈列馆',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ShuZiChengLieGuan.png',
    summary: '通过触控大屏和多媒体设备，生动展示刑警支队的光辉历史与成就。',
    background: '刑警支队的历史、成就和荣誉是宝贵的精神财富。传统的实物陈列方式空间有限、形式单一，难以生动、全面地进行展示和宣传教育。',
    solution: '数字陈列馆是以刑警支队文化为核心，集展示、互动、查询于一体的数字化平台：\n多媒体展示：系统支持照片、视频、录音等多种媒体形式，立体化地展示刑警支队的发展历程、重大案件、英雄事迹等。\n互动体验：平台部署于触控大屏、移动广告机等互动设备，观众可通过触摸操作，自主浏览感兴趣的内容。\n档案数字化管理：实现了对案件、嫌疑人、证人等档案的电子化管理和安全查询。',
    value: '数字陈列馆是新时代加强警队文化建设的重要载体。它通过引人入胜的互动体验，向内部民警和外部观众生动地展示了刑警精神，起到了极佳的宣传教育和荣誉激励作用。'
  }
])

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>智慧法治</h1>
    </div>

    <div class="content">
      <div class="section-description">
        <p>科技赋能政法，助推平安中国建设。</p>
      </div>

      <div class="cards-grid">
        <div
          v-for="case_ in smartLawCases"
          :key="case_.id"
          class="card-item"
          @click="showCaseDetail(case_)"
        >
          <div class="card-image">
            <img :src="case_.image" :alt="case_.title" />
          </div>
          <div class="card-content">
            <h3>{{ case_.title }}</h3>
            <p>{{ case_.summary }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="dialogVisible" class="dialog-overlay" @click="closeDialog">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h2>{{ currentCase?.title }}</h2>
          <button class="close-btn" @click="closeDialog">
            <el-icon><Close /></el-icon>
          </button>
        </div>
        <div class="dialog-body">
          <div class="case-detail-image">
            <img :src="currentCase?.image" :alt="currentCase?.title" />
          </div>
          <div class="case-detail-content">
            <div class="detail-section">
              <h3>项目背景</h3>
              <p>{{ currentCase?.background }}</p>
            </div>
            <div class="detail-section">
              <h3>解决方案</h3>
              <p v-for="(line, index) in currentCase?.solution?.split('\n')" :key="index">
                {{ line }}
              </p>
            </div>
            <div class="detail-section">
              <h3>平台价值</h3>
              <p>{{ currentCase?.value }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
}

.header {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.section-description {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.section-description p {
  font-size: 1rem;
  color: #666;
  margin: 0;
  font-weight: 500;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.card-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e5e7eb;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #1693d2;
}

.card-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  background: #f8fafc;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-item:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 1.25rem;
}

.card-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.card-content p {
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.dialog-content {
  background: white;
  border-radius: 1rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.dialog-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.dialog-body {
  overflow-y: auto;
  padding: 1.5rem;
}

.case-detail-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  background: #f8fafc;
}

.case-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1693d2;
  margin: 0 0 0.75rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e5e7eb;
}

.detail-section p {
  font-size: 0.95rem;
  color: #4b5563;
  line-height: 1.6;
  margin: 0 0 0.5rem 0;
}

.detail-section p:last-child {
  margin-bottom: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-image {
    height: 160px;
  }

  .dialog-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .dialog-header {
    padding: 1rem;
  }

  .dialog-body {
    padding: 1rem;
  }

  .case-detail-image {
    height: 150px;
  }
}
</style>
