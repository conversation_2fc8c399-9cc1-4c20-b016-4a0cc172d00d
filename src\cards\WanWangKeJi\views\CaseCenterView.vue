<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import {
  ArrowRight,
  Setting,
  Close
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

// 类型定义
interface CaseData {
  id: number
  title: string
  image: string
  summary: string
  background?: string
  solution?: string
  value?: string
  overview?: string
  design?: string
  technology?: string
  hasVisitButton?: boolean
  isIntranet?: boolean
  description?: string // 简介
}

interface SolutionSection {
  title: string
  description: string
  icon?: any
  image: string // 封面图
  cases: CaseData[]
}

interface WebsiteCategory {
  title: string
  description: string
  image: string // 封面图
  cases: CaseData[]
}

type SectionKey = 'smartLaw' | 'smartGovernance' | 'smartAffairs' | 'smartLife' | 'smartManagement'
type WebsiteKey = 'government' | 'police' | 'enterprise' | 'institution' | 'hospital'

const router = useRouter()

const goBack = () => {
  router.push('/card/WanWangKeJi')
}

// 添加访问网站的方法
const visitWebsite = () => {
  // 根据当前查看的案例决定跳转到哪个网站
  if (currentCase.value) {
    // 政府机关网站
    if (currentCase.value.title.includes('信州区')) {
      window.open('https://www.jxxz.gov.cn/', '_blank')
    } else if (currentCase.value.title.includes('广丰区')) {
      window.open('https://www.gfx.gov.cn/gfx/index.shtml', '_blank')
    } else if (currentCase.value.title.includes('万年县')) {
      window.open('http://www.zgwn.gov.cn/zgwn/index.shtml', '_blank')
    } 
    // 公安机关网站
    else if (currentCase.value.title.includes('上饶市公安局刑事警察支队')) {
      window.open('https://www.zgsr.gov.cn/gaj/bmjs/202202/e86a3756e5094202ac2ffe5cb5d98f73.shtml', '_blank')
    } else if (currentCase.value.title.includes('三清山公安')) {
      window.open('https://www.zgsr.gov.cn/gaj/', '_blank')
    } else if (currentCase.value.title.includes('横峰县公安')) {
      window.open('https://www.zgsr.gov.cn/gaj/', '_blank')
    } else if (currentCase.value.title.includes('广丰公安')) {
      window.open('https://www.zgsr.gov.cn/gaj/', '_blank')
    }
    // 国企/集团网站
    else if (currentCase.value.title.includes('上饶国控投资集团')) {
      window.open('https://www.srgktzjt.com/ ','_blank')
    } else if (currentCase.value.title.includes('上饶文旅集团')) {
      window.open('http://www.srlyjt.com/', '_blank')
    } else if (currentCase.value.title.includes('江西鑫邦实业集团') || currentCase.value.title.includes('鑫邦集团')) {
      window.open('http://www.jxxbjt.com/', '_blank')
    } else if (currentCase.value.title.includes('际洲集团')) {
      window.open('https://www.jzjt.com/', '_blank')
    } else if (currentCase.value.title.includes('上饶市医疗投资集团')) {
      window.open('http://www.sryltz.com/', '_blank')
    }
    // 企事业单位网站
    else if (currentCase.value.title.includes('上饶市广信区市政公用服务中心')) {
      window.open('http://www.gxsz.gov.cn/', '_blank')
    } else if (currentCase.value.title.includes('广信工投集团')) {
      window.open('http://www.srgxgt.com/', '_blank')
    } else if (currentCase.value.title.includes('上饶市文化创意产业')) {
      window.open('http://www.srwlj.cn/', '_blank')
    } else if (currentCase.value.title.includes('上饶市科学技术协会')) {
      window.open('http://kx.shangrao.gov.cn/', '_blank')
    }
    // 医院/学校网站
    else if (currentCase.value.title.includes('上饶市人民医院')) {
      window.open('http://www.srsrmyy.cn/', '_blank')
    } else if (currentCase.value.title.includes('上饶市第三人民医院')) {
      window.open('http://www.srsdsrmyy.com/', '_blank')
    } else if (currentCase.value.title.includes('上饶卫校附属医院') || currentCase.value.title.includes('广丰区妇幼保健院')) {
      window.open('http://www.srwxfsyy.com/', '_blank')
    } else if (currentCase.value.title.includes('上饶中学')) {
      window.open('http://www.jxsrzx.com/', '_blank')
    } else if (currentCase.value.title.includes('上饶卫生学校')) {
      window.open('http://www.srwsxx.cn/index.html', '_blank')
    }
    // 默认跳转
    else {
      window.open('https://www.jxxz.gov.cn/', '_blank')
    }
  }
}

// 移动端检测
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 当前激活的Tab - 手机端默认显示门户网站建设案例
const activeTab = ref<'solutions' | 'websites'>('solutions')

// 当前激活的解决方案分类
const activeSolutionSection = ref<SectionKey | null>(null)

// 当前激活的网站分类
const activeWebsiteSection = ref<WebsiteKey | null>(null)

// 弹窗状态
const dialogVisible = ref(false)
const currentCase = ref<CaseData | null>(null)

// 切换解决方案分类
const toggleSolutionSection = (section: SectionKey) => {
  activeSolutionSection.value = activeSolutionSection.value === section ? null : section
}

// 切换网站分类
const toggleWebsiteSection = (section: WebsiteKey) => {
  activeWebsiteSection.value = activeWebsiteSection.value === section ? null : section
}



// 显示案例详情
const showCaseDetail = (caseData: CaseData) => {
  currentCase.value = caseData
  dialogVisible.value = true
}

// 显示网站详情
const showWebsiteDetail = (websiteData: CaseData) => {
  currentCase.value = websiteData
  dialogVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
  currentCase.value = null
}

// 解决方案数据
const solutions = reactive<Record<SectionKey, SolutionSection>>({
  smartLaw: {
    title: '智慧法治',
    description: '科技赋能政法，助推平安中国建设。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuifazhi.jpeg',
    cases: [
      {
        id: 1,
        title: '公安反诈研判系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/FanZhaYanPanXiTong.png',
        summary: '打造全流程反诈作战平台，有效提升预警与打击效率。',
        background: '随着电信网络诈骗犯罪日益猖獗，传统侦查手段在海量信息面前显得力不从心。为有效遏制犯罪势头，亟需一套能够全面、实时、准确分析研判辖区内案事件特点和规律的智能化平台，以实现"预防、发现、打击犯罪"的战略目标。',
        solution: '江西万网基于先进的云服务体系，打造了统一的大数据平台，整合各类内外部关联数据，构建了三大核心智慧应用：\n智慧情报研判：通过多维度数据碰撞和智能分析，挖掘潜在犯罪线索，精准刻画犯罪团伙画像。\n内部公文共享：打破信息壁垒，实现跨部门、跨层级的情报、指令、案卷等信息高效流转与共享。\n智慧求助管理：建立统一的受害人求助入口，规范处置流程，提升群众满意度。',
        value: '本系统是实现反诈工作智能化研判的"利器"，它成功将海量、零散的数据转化为高价值的情报，实现了对数据的智能管控、深度挖掘和高效服务，为信息化作战提供了强有力的技术支撑。'
      },
      {
        id: 2,
        title: '公安反诈举报平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/FanZhaJuBao.png',
        summary: '构建警民协作的线上反诈阵地，方便群众快速举报涉诈线索。',
        background: '电信网络诈骗具有隐蔽性强、传播速度快等特点，仅靠公安机关单方面打击难以完全遏制。发动群众、依靠群众，建立一个便捷、高效的线上举报渠道，对于拓宽线索来源、精准预警防范至关重要。',
        solution: '江西万网开发的反诈举报平台，是一个集举报、宣传、管理于一体的综合性系统：\n便捷举报入口：群众可通过小程序随时随地上报涉诈线索，支持文字、图片、录音等多种形式。\n智能化后台：后台系统能自动对举报信息进行分类、归集，并根据区域、案件类型进行大数据分析，为精准打击和防范宣传提供数据支持。\n闭环式反馈：群众提交的举报，系统会自动流转至对应权限的民警处理，处理进度和结果会实时反馈给举报人，形成管理闭环。',
        value: '该平台成功构建了"全民参与、共创平安"的反诈新格局。它不仅是一个线索收集工具，更是连接警民的桥梁，通过精准的数据分析，让反诈宣传更具针对性，有效降低了辖区发案率。'
      },
      {
        id: 3,
        title: '公安警保管理系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/JinBaoGuanLi.png',
        summary: '实现装备、资产、维修、采购等警保业务全流程信息化管理。',
        background: '传统警务保障工作多依赖纸质记录和线下审批，存在数据查询难、流程耗时长、资产底数不清、资源浪费等问题，难以适应现代化警务工作的快节奏要求。',
        solution: '本系统将分散的装备、资产、维修、采购、车辆、基建等数据统一整合，实现了"六大核心功能"：\n资产/库存管理：建立全局统一的资产电子档案，实时掌握资产状态与库存情况。\n车辆/维修管理：实现车辆使用调度、维修保养线上申请与审批，全过程留痕。\n采购/申领管理：规范物资采购与申领流程，从申请、审批到发放，全程线上化。\n数据统计与分析：自动生成各类统计报表，为预算制定和资源调配提供数据决策支持。',
        value: '系统通过"互联网+现代化管理"，彻底改变了传统警保工作的弊端。它规范了流程、减少了人员跑动、实现了资产动态展现和资源的合理调配，极大提升了警务保障工作的实战化和专业化水平。'
      },
      {
        id: 4,
        title: '公安流动人口管理系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/LiuDongRenKou.png',
        summary: '打通内网数据，实现对流动人口的精准、高效、安全管理。',
        background: '城市流动人口数量庞大、流动性强，传统的人工登记和排查方式存在效率低、信息不准、安全隐患发现不及时等问题。如何准确掌握辖区内流动人口和出租屋情况，是基层警务工作的一大挑战。',
        solution: '本系统通过打通公安内网数据，为基层民警的移动端赋能：\n精准数据比对：实时将民警走访采集的人员信息与内网的图像识别库、犯罪嫌疑人库进行比对，发现风险立即预警。\n移动化办公：民警通过手机即可完成信息采集、风险预警接收、任务处理等工作，极大提升了走访排查效率。\n全方位支持：系统为政府部门全面、准确掌握辖区出租屋和流动人口状况提供了强有力的支持，并为多部门协同管理提供了信息化基础。',
        value: '系统将"汗水警务"升级为"智慧警务"，为基层民警走访排查提供了可靠的数据依据和高效的工具，让安全隐患无处遁形，有力支撑了城市的安全管理。'
      },
      {
        id: 5,
        title: '公安大走访系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/GongAnZouFang.png',
        summary: '实现全警大走访工作的信息化采集、分配、管理与统计。',
        background: '"保平安、促和谐"的全警大走访活动，涉及面广、采集信息量大。传统纸质表格填写和手动汇总的方式效率低下，数据难以利用，且流程无法追溯。',
        solution: '本系统将大走访工作全流程线上化、数字化：\n移动信息采集：民警通过手机即可采集走访对象的家庭情况、意见建议、现场照片（含定位）等信息，数据实时上传。\n任务智能分配：系统支持在线分配走访任务，方便警务人员实时查看当日、当月的任务进度，并可导出标准化的走访表格。\n层级化权限管理：不同层级的领导拥有不同范围的数据查看和管理权限，确保信息安全和管理的有序性。\n大数据统计分析：系统能自动对走访数据进行统计分析，直观展示各辖区的工作进展和社情民意。',
        value: '系统为"全警爱民实践大走访"活动提供了有力的技术保障，实现了"底数清、情况明"。它不仅提高了工作效率，更拉近了警民关系，是构建和谐警民环境的重要举措。'
      },
      {
        id: 6,
        title: '公安网格云助手',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/GongAnWangGeYunZhuShou.png',
        summary: '基于"移动互联网+"打造的微网格警民问题解决平台。',
        background: '基层社区的群众求助和问题反馈渠道不畅，传统管理模式响应慢、责任不清。如何快速响应和解决人民群众的"急难愁盼"问题，是提升基层治理能力的关键。',
        solution: '"公安网格云助手"平台通过开放式的组织模式和游戏化的激励机制，重塑了问题解决流程：\n快速上报与统一管理：群众可通过小程序快速上报问题，系统统一接收并流转至对应网格民警。\n创新"任务抢单"机制：引入积分激励和任务抢单模式，激发民警解决问题的主动性和积极性。\n层级化监督：分管、主管领导可在线监督任务处理的全过程，确保问题得到有效解决。',
        value: '平台将传统管理模式升级为"开放式"组织结构，以"互联网+网格民警"的模式，为快速解决群众反馈的问题提供了实用、高效的解决方案，是服务型政府建设的生动实践。'
      },
      {
        id: 7,
        title: '刑警支队数字陈列馆',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ShuZiChengLieGuan.png',
        summary: '通过触控大屏和多媒体设备，生动展示刑警支队的光辉历史与成就。',
        background: '刑警支队的历史、成就和荣誉是宝贵的精神财富。传统的实物陈列方式空间有限、形式单一，难以生动、全面地进行展示和宣传教育。',
        solution: '数字陈列馆是以刑警支队文化为核心，集展示、互动、查询于一体的数字化平台：\n多媒体展示：系统支持照片、视频、录音等多种媒体形式，立体化地展示刑警支队的发展历程、重大案件、英雄事迹等。\n互动体验：平台部署于触控大屏、移动广告机等互动设备，观众可通过触摸操作，自主浏览感兴趣的内容。\n档案数字化管理：实现了对案件、嫌疑人、证人等档案的电子化管理和安全查询。',
        value: '数字陈列馆是新时代加强警队文化建设的重要载体。它通过引人入胜的互动体验，向内部民警和外部观众生动地展示了刑警精神，起到了极佳的宣传教育和荣誉激励作用。'
      }
    ]
  },
  smartGovernance: {
    title: '智慧治理',
    description: '数字驱动社会治理，提升城市管理效能。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuizhili.jpeg',
    cases: [
      {
        id: 1,
        title: '重点人员管控平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhongDianRenYuanGuanKong.png',
        summary: '对涉诈等重点人员进行数字化、人性化的全周期闭环管控。',
        background: '为有效铲除电信诈骗滋生土壤，需对涉诈重点人员进行长效管控。传统人盯人模式耗费大量警力且效果有限，亟需一套智能化的技术手段，实现对人员的精准、高效、人性化管理。',
        solution: '本平台通过"大数据+小程序"的模式，实现对重点人员的动态管控：\n风险评定与分级：根据人员前科、现实表现等信息，自动评定风险等级，实施差异化管控措施。\n多维管控手段：包含定时打卡、人脸识别、电子围栏（活动区域划定）、双重审批等功能。\n创新积分机制：采用积分制管理，被管控人员可通过良好表现获取积分，达到安全等级后即可解除管控，有效提高其配合度。\n可视化管理：市、县两级可通过数据大屏直观掌握辖区内所有重点人员的分布、状态和轨迹，实现"一屏观全域"。',
        value: '系统在降低诈骗率的同时，极大减轻了基层民警的监管负担。创新的积分制提高了被管控人员的配合意愿，实现了从"被动监管"到"主动配合"的转变，是推进社会和谐安定的重要技术创新。'
      },
      {
        id: 2,
        title: '矛盾纠纷化解平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/MaoDunJiuFenHuaJiePingTai.png',
        summary: '打造一站式矛盾纠纷线上化解平台，整合多方力量高效调解。',
        background: '随着社会发展，大量非警务类的矛盾纠纷涌入110，占用了宝贵的警力资源。传统调解模式又存在渠道单一、信息不通、效率低下等问题，难以应对新时期复杂多样的纠纷类型。',
        solution: '本平台旨在通过技术手段，重塑矛盾纠纷的处置流程：\n智能分派系统：调解中心可根据警情案由、诉求等信息，一键分派给最合适的调解员（如法院、平安义警、网格员等），实现专业的人做专业的事。\n"一群一案"在线调解室：为每起纠纷建立专属在线沟通群，当事人、调解员可随时随地进行视频、语音、文字沟通，所有沟通记录均可作为调解档案。\n大数据展示：平台运用大数据分析，通过热力图、统计图等形式，实时展示各区域、各类型的纠纷发生情况，为预测预警、排查风险提供科学依据。',
        value: '平台有效分流了非警务警情，解放了警力，同时整合了社会各界的调解力量，提高了纠纷化解的效率和成功率。它不仅是一个案件处置工具，更是一个社会矛盾的"减压阀"和"稳压器"。'
      },
      {
        id: 3,
        title: '舆情督办系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YuQingDuBan.png',
        summary: '实现舆情从发现、上报、处置到反馈的全流程闭环管理。',
        background: '在网络时代，舆情发酵速度快，处置稍有不慎便会引发危机。传统的邮件、电话督办方式存在流程不清晰、责任不明确、进度难跟踪等问题，难以适应高效的舆情处置要求。',
        solution: '"舆情督办系统"是为了提高舆情处置效率而开发的"舆情任务管理器"：\n全流程线上化：提供舆情的上报、下发、流程监控、统计分析等全链条功能，所有操作在线完成。\n任务闭环管理：每条舆情作为一个任务，明确责任人、处置时限，办理过程全程留痕，有效防止工作积压和贻误。\n移动端优先：系统以移动端为核心，相关人员可随时随地接收任务、处理舆情、上报进展，打破了时间和空间的限制。\n点到系统联动：整合点到功能，可实时查看相关人员的在岗情况，确保舆情处置任务能第一时间响应。',
        value: '系统将复杂的舆情处置工作流程化、规范化、透明化，极大地提高了处置效率和协同能力。它通过对工作任务的分解、下派和过程监督，有效减少了工作汇报层级，是政府和企事业单位进行危机管理的得力助手。'
      },
      {
        id: 4,
        title: '"三重一大"信息监管平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/SanZhongYiDa.png',
        summary: '对"三重一大"事项进行全过程、全方位、实时性的信息化监管。',
        background: '"三重一大"（重大事项决策、重要干部任免、重大项目安排、大额资金使用）是权力运行的关键环节，传统监督方式存在滞后性、信息不对称等问题，难以有效防控廉政风险和决策风险。',
        solution: '本平台运用信息技术手段，实现了对"三重一大"事项的在线化、透明化监管：\n决策过程全记录：从事项目录上报、事项清单上报，到决策会议启动、表决过程管理，再到决议生成与归档，实现决策过程全程留痕、可追溯。\n执行过程全监控：对决策事项的执行过程进行实时跟踪，设置预警机制，一旦出现偏差或异常，系统立即提醒，便于及时纠偏。\n多层级协同监督：系统支持党风政风监督室、纪检监察室、纪检监察组等多个监督主体协同工作，形成监督合力。',
        value: '平台通过技术手段，有效提高了决策的科学性、规范性和透明度，降低了决策风险和廉政风险。它实现了从"结果监督"到"过程监督"的转变，是推动组织健康、稳定发展的"数字防火墙"。'
      },
      {
        id: 5,
        title: '义警大数据管理平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YiJingDaShuJuGuanLi.jpg',
        summary: '实现统一招募、统一标准、统一培训、统一管理、统一考核的义警管理模式闭环。',
        background: '传统义警队伍管理模式存在招募渠道不统一、培训考核标准不一、活动组织效率低下等问题。为构建共建共治共享的社会治理新格局，需借助"移动互联网+"技术，打造一个智慧化、便捷化的义警管理与服务平台。',
        solution: '江西万网打造的义警大数据管理平台，实现了"五大统一"和"两大激励"：\n统一管理：实现统一招募、统一标准、统一培训、统一管理、统一考核的管理模式闭环。\n激励机制：建立"爱心激励"与"爱心传递"机制，通过积分、排行等方式，提升义警参与感与荣誉感。\n多端覆盖：系统支持调度大屏、PC后台、手机小程序等多终端，满足不同场景下的使用需求。',
        value: '人性化参与：平台面向全社会开放，无论是公职人员还是普通市民，都能便捷地参与公益事业。\n功能全覆盖：包含27个子系统、120个功能模块，全面覆盖了从协会介绍、活动报名到线上培训、服务时长统计的全过程。\n模式创新：将传统管理升级为"开放式、民主化"的组织模式，有效破解了义警管理难题，为市域社会治理创新提供了实用方案。'
      }
    ]
  },
  smartAffairs: {
    title: '智慧政务',
    description: '赋能政府数字化，构建高效服务型政府。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuizhengwu.jpeg',
    cases: [
      {
        id: 1,
        title: '智慧党建云平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiDangJianYun.png',
        summary: '集管理、学习、互动于一体的综合性党建平台，实现数字化、在线化、智能化管理。',
        background: '传统党建工作存在形式单一、时空限制多、党员参与度不高等问题。为适应新时代党建要求，需利用"互联网+"技术，将党建工作从线下搬到线上，实现数字化、在线化、智能化管理。',
        solution: '江西万网打造的智慧党建云平台，是一个集管理、学习、互动于一体的综合性平台：\n多终端覆盖：提供PC管理端、党员手机端APP、数据监控大屏、线下触控一体机等多种终端，满足不同场景需求。\n核心功能矩阵：涵盖党建宣传、组织生活管理（三会一课、主题党日）、党员管理、在线学习考试、党内服务等核心模块。\n大数据驱动：将各党组织的工作情况通过数据图表直观展现，实现"让数据自己说话"，为党建工作的督促、检查、催办提供决策支持。',
        value: '平台显著提升了党组织的管理效率和党员的参与感，通过技术手段解决了传统党建工作的诸多痛点，激发了网络空间的正能量，有效保持了党的先进性，提升了组织的引领能力。'
      },
      {
        id: 2,
        title: '智慧工会服务云平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiGongHui.png',
        summary: '构建线上线下融合的职工服务体系，提升工会服务效能。',
        background: '为适应新时期工会服务发展与创新的要求，需不断扩大工会组织覆盖面，激发基层工会活力。传统的线下服务模式难以满足广大职工日益增长的个性化、便捷化、精准化需求，亟需构建一个智能化的线上服务体系。',
        solution: '江西万网推出的"智慧工会服务云平台"，是一个集服务、维权、管理于一体的综合性职工服务平台：\n线上线下一体化运营：创新的将线上服务终端（小程序/APP）与线下职工服务中心相结合，打造了全国独一无二的一体化运营服务体系，实现了服务的无缝衔接。\n个性化普惠服务：以工会会员为中心，提供困难职工帮扶申请、基层工会法人变更、职工互助保障参保与理赔、活动报名、公益课堂等一系列个性化的普惠服务，让服务触手可及。\n社会资源深度整合：积极引入社会力量，整合各类商家、机构资源，为工会会员提供消费优惠、法律援助、技能培训等多元化服务，极大地丰富了工会的服务内涵。\n构建"造血"机制：平台坚持"政府主导，市场运营"的模式，通过整合区域资源，打造平台自身的盈利能力，实现了平台的可持续发展。',
        value: '该平台通过便捷化、常态化的线上服务，极大地提升了工会的服务效能和在职工群众中的影响力，切实提升了职工的幸福指数和获得感。同时，平台的大数据功能能够深入挖掘职工需求，为工会工作的精准决策提供了有力支持，推动了工会组织向服务型、学习型、创新型的"新三型"组织转型。'
      },
      {
        id: 3,
        title: '辅警教育训练考试管理平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/FuJinJiaoYuKaoShi.jpg',
        summary: '为辅警队伍打造的集在线学习、训练、考试、评估于一体的综合性平台。',
        background: '辅警队伍是公安工作的重要辅助力量，其职业素质和业务能力的持续提升至关重要。传统的线下集中培训和考试模式，存在组织成本高、工学矛盾突出、考核评估效率低等问题。',
        solution: '本平台是一套针对辅警人员进行线上考试与教育培训的综合解决方案：\n灵活的考试模式：系统支持正式考试（在规定时间内完成）和模拟考试（用于查漏补缺）两种模式，并提供顺序练题、错题重做等多种学习方式，满足辅警不同的学习需求。\n便捷的题库与试卷管理：管理员可通过后台轻松实现题目录入（支持批量导入）、试卷创建（智能组卷或手动组卷）、考试发布等操作，四步即可快速组织一场线上考试。\n智能化的考务功能：系统具备智能判卷、成绩排名、错题自动归集等功能，极大地减轻了考务人员的工作负担。\n全面的数据分析：系统能自动生成多维度的考试数据分析报表，帮助管理者全面了解辅警队伍的知识掌握情况，为后续的培训工作提供决策依据。',
        value: '该平台为辅警队伍提供了一个高效、便捷的线上教育与培训渠道。它有效解决了传统培训模式的痛点，促进了辅警职业素质和业务能力的持续提升，是加强辅警队伍规范化、专业化建设的重要技术支撑。'
      }
    ]
  },
  smartLife: {
    title: '智慧生活',
    description: '科技便利民生，开启智慧城市生活。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuishenghuo.jpeg',
    cases: [
      {
        id: 1,
        title: '上饶青年卡',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ShangRaoQingNianKa.png',
        summary: '为青年人才提供便捷的住宿预订和综合服务的数字化平台。',
        background: '为吸引和留住青年人才，上饶市推出了青年人才驿站，但线下申请流程繁琐，房源信息不透明。为给青年人才提供更便捷、更优质的服务，亟需一个数字化的线上预约和管理平台。',
        solution: '"上饶青年卡"小程序平台，以"凝聚人才、服务企业"为宗旨，提供了一站式解决方案：\n全流程线上化：实现从线上浏览房源、预约房间，到酒店审批、前台核实、拎包入住的全流程数字化管理。\nPMS系统对接：与酒店管理系统（PMS）无缝对接，实时同步房型、房价、订单等数据，确保信息准确无误。\n灵活定价策略：后台支持根据周末、旺季、节假日等不同时段，灵活调整房间价格，满足精细化运营需求。',
        value: '平台上线后，有效整合了上百套优惠房源，极大便利了来饶求职、创业的青年人才。它不仅是一个住宿预订工具，更是一个集成了就业指导、政策推送、团青活动等功能的综合服务平台，成为服务青年、助力发展的重要载体。'
      },
      {
        id: 2,
        title: '场馆选座票务预约系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/PiaoWuYuYue.png',
        summary: '为体育比赛、文艺演出等活动提供全流程在线化票务解决方案。',
        background: '传统票务销售模式存在诸多痛点：观众需前往售票点排队购票，耗时耗力；主办方人工成本高，且易出现假票、黄牛票等问题；财务对账流程繁琐，容易出错。',
        solution: '江西万网开发的"场馆选座票务预约系统"，是一个专门为体育比赛或各类演出活动提供在线售票服务的应用程序：\n便捷的购票体验：用户可随时随地通过小程序浏览赛事/演出信息，在线选择场次和座位，并通过微信、支付宝等多种方式完成支付，全程无需排队。\n信息透明化：系统实时更新剩余票数和座位信息，让用户能够第一时间掌握购票情况。\n高效的票务管理：主办方可通过后台轻松管理票务信息，设置不同票价，并实时查看销售数据。\n安全的核销入场：用户购票后生成电子二维码，现场通过扫码设备快速核销入场，杜绝了假票问题，并大大提升了入场效率。',
        value: '该系统通过数字化的方式，极大地简化了传统票务的销售与入场流程，为观众和主办方双方都带来了极大的便利。它不仅提升了出票效率和支付效率，更通过无纸化流程，助力环保和二次消费的提升，是一种高效且经济的现代票务解决方案。'
      },
      {
        id: 3,
        title: '新冠疫苗预约接种平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YiQingFangKong.png',
        summary: '为市民提供便捷、高效的新冠疫苗在线预约与信息查询服务。',
        background: '在大规模疫苗接种工作中，线下排队容易造成人群聚集，增加感染风险，且接种效率不高。市民也难以获取准确的疫苗库存和接种点信息。',
        solution: '这是一个便捷、高效的在线疫苗服务系统，旨在解决疫苗接种过程中的信息不对称和排队问题：\n市民端（小程序）：市民可在线完成个人信息登记，查看附近接种点的地址、服务时间、疫苗库存等信息，并根据自己的时间安排，选择合适的时段进行预约。预约成功后会生成预约码。\n管理端（PC后台）：信息全掌握：市、区、县三级卫健委可通过后台全面掌握辖区内接种数据，对预接种人员进行统一安排和管理。站点有效管理：管理员可方便地创建和管理各接种站点的信息，发布公告。智能短信提醒：用户预约成功后，系统会自动发送提醒短信，并可在接种前一天再次提醒，有效避免用户错过接种时间。',
        value: '平台通过"分时段预约"模式，有效分流了接种人群，避免了现场拥堵，大大提高了疫苗接种效率和安全性。它为公众提供了丰富的疫苗信息和健康咨询，为政府部门提供了强大的数据管理和调度能力，是科技抗疫的成功实践。'
      },
      {
        id: 4,
        title: '全域地图智慧旅游',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/QingYuLvYou.png',
        summary: '为游客提供智能化的自助导览服务，提升景区游玩体验。',
        background: '传统旅游模式中，游客常常面临路线规划难、景点信息获取滞后、容易迷路等问题。景区也存在客流引导不畅、景点拥堵等管理难题。',
        solution: '智慧导览系统是通过电子导览设备或小程序，为游客提供智能化、自助化服务的网络控制系统：\n自定义/手绘地图：根据景区特色，绘制精美的手绘地图，并在地图上标注景点、停车场、厕所、商店、餐饮等关键地点信息，一目了然。\n个性化路线推荐：系统可根据游客的兴趣和时间，推荐如"古村文化游"、"山水观光游"、"研学拓展游"等不同主题的个性化游览路线。\n沉浸式景点展示：通过高清图片、短视频、VR全景等多种方式，生动展示核心景点的历史背景、文化内涵和特色景观，让游客未到先知，身临其境。\n语音导览与讲解：当游客到达某个景点时，系统可自动触发语音讲解，提供生动的导览服务。',
        value: '该系统极大地提升了游客的游玩体验，解决了传统旅游中的诸多不便。对景区而言，它实现了智慧化的客流引导，有效防止了拥堵，提升了管理效率和品牌形象，是"旅游+科技"深度融合的产物。'
      },
      {
        id: 5,
        title: '答题小程序',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/DaTiXiaoChengXu.png',
        summary: '提供一个可随时随地进行学习、竞赛、考核的线上答题平台。',
        background: '传统知识竞赛和学习考核活动组织流程复杂、成本高，且受时间地点限制。需要一个轻量化、易于传播的线上平台，来满足各类学习和答题需求。',
        solution: '答题小程序是一个灵活、易用的线上学习教育平台：\n灵活的主题切换：系统前端支持根据不同活动主题（如党建知识、科普知识、安全生产规范等）一键切换界面风格和题库内容。\n强大的考试功能：支持题库随机抽题、考试时间限制、练习模式、成绩实时排名、系统自动阅卷等核心功能。\n便捷的创建与分享：管理员通过后台"三步"（导入考卷、设置规则、发布链接）即可轻松创建一场考试，并通过微信快速分享给考生。\n智能的数据分析：后台可实时查看所有考生的考试情况，自动统计正确率、得分分布等，并支持原始答卷一键下载。',
        value: '答题小程序以其便捷、灵活、低成本的优势，成为各类组织开展知识竞赛、业务考核、课后练习、招聘培训等活动的理想工具。它将学习和考核变得简单、有趣，极大地拓宽了知识传播的渠道。'
      }
    ]
  },
  smartManagement: {
    title: '智慧管理',
    description: '精细化管理系统，提升企业园区效率。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhihuiguanli.jpeg',
    cases: [
      {
        id: 1,
        title: '智慧园区系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiYuanQu.jpg',
        summary: '集"感知-分析-决策-行动"于一体的综合园区管理平台。',
        background: '传统园区管理存在数据孤岛、管理效率低下、安全隐患发现不及时等问题。为提升园区管理的科学化、智能化水平，需构建一个能够融合多维数据、实现一体化管控的智慧平台。',
        solution: '江西万网打造的智慧园区系统，是一个集"感知-分析-决策-行动"于一体的综合管理平台：\n数字孪生：运用3D建模技术，构建园区的三维可视化模型，对园区产业、资产、设施、安防等关键指标进行综合监测。\n物联感知：集成摄像头、烟感、充电桩等各类物联网传感器，对设备运行状态和环境进行实时监控与预警。\n一体化架构：打破各子系统壁垒，构建统一的数据底座和应用平台，实现园区管理的"一张图"和多方联动。',
        value: '系统广泛适用于工业、物流、办公等各类园区，通过科技赋能，深入业务场景，实现了从简单的连接到智能的体验与管理。它不仅提升了园区的运营效率和安全水平，其标准化架构也支持集团客户进行多园区的统一构建与管理。'
      },
      {
        id: 2,
        title: '疫情防控实战平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YiQingFangKongShiZhanPingTai.png',
        summary: '整合多源数据，实现从发现、核查到管控全流程闭环的实战指挥平台。',
        background: '新冠疫情具有传播快、涉及面广的特点，传统的人工排查和信息上报方式存在效率低、数据不准、流程脱节等问题，难以应对大规模、快节奏的疫情防控需求。',
        solution: '本平台是基于"移动互联网+大数据"技术打造的一站式疫情防控指挥系统：\n多源数据整合：能够整合省漫游数据、落地检数据、社区报备数据等，通过智能比对和去重，快速筛选出高风险人员。\n任务快速分派与闭环：平台将核查任务通过手机端快速派发至社区网格员，实现"秒级响应"。从任务下发、上门核查、转运隔离到解除管控，全程线上化、可追溯，责任落实到人。\n移动化赋能基层：网格员通过手机即可完成人员信息核查、同住人报备、核酸采样提醒、疾控告知书下发等工作，极大提升了一线工作效率。\n可视化指挥调度：通过数据大屏实时展示辖区内核查进度、隔离点状态、风险人员分布等关键信息，为指挥决策提供强有力的实时数据支持。',
        value: '平台实现了"信息实时共享、工作流程闭环、责任精准到人"，将原来杂乱无章的防疫工作变得井然有序、高效透明，极大提升了流调溯源和社区管控的效率，是科技抗疫的成功典范。'
      },
      {
        id: 3,
        title: '智慧消防系统',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/ZhiHuiXiaoFang.png',
        summary: '针对"九小场所"等消防重点单位的数字化、智能化安全管理系统。',
        background: '"九小"场所（小商铺、小作坊等）因安全意识淡薄、消防设施不到位，成为火灾高发的重灾区。传统的人工巡查方式覆盖面有限、效率低，且数据无法留存和分析。',
        solution: '智慧消防安全管理平台，通过"一户一码"的模式，实现了对消防重点单位的精细化、智能化管理：\n"一户一码"数字档案：为每个场所生成专属二维码，扫码即可查看该场所的人员住宿、用火用电、消防设施、疏散通道等全部消防安全信息。\n智能化巡检：消防巡查人员通过手机扫码进行巡检，巡检记录和隐患照片实时上传，替代了传统的纸质台账，实现了巡检工作的智能化和无纸化。\n设备设施全周期管理：系统建立消防设备设施的电子档案，结合日常巡检、维修、保养数据，综合分析其"健康状况"，并对即将到期的设备进行预警。\n一键报警与联动：一旦发生火情，场所负责人或巡查人员可一键报警，系统会自动将火情位置、场所信息等发送至消防管理部门，实现快速响应。',
        value: '该系统通过技术手段，将消防安全管理责任落实到每个场所，将被动的"亡羊补牢"转变为主动的"防患于未然"。它极大地提升了消防监管效率和火灾隐患的排查治理能力，是保障人民生命财产安全的重要科技防线。'
      },
      {
        id: 4,
        title: '阳光招采平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/YangGuangZhaoCai.png',
        summary: '帮助招标企业寻求优质供应商，降低采购成本的线上招采平台。',
        background: '传统的线下招标采购流程耗费大量人力物力，存在信息不对称、决策依据不足、寻源范围窄等问题，难以实现"货比三家"，与阳光、廉洁、高效的采购要求相去甚远。',
        solution: '阳光招采平台是一个致力于帮助招标企业降低成本、提升效率的线上化、自动化采购平台：\n高效信息发布：招标企业可在线快速发布和分享招标信息，极大地提升了招标公告的传播力和覆盖面。\n海量供应商库：平台汇聚了大量的优质供应商，招标企业可以从中进行比选，有机会选择到性价比最高的合作伙伴。\n便捷在线投标：供应商可随时随地通过平台参与线上报名和投标，打破了地域和时间的限制。\n办公自动化集成：平台将传统的采购流程与现代办公自动化相结合，实现了采购全流程的数字化管理。',
        value: '电子招采平台在提升企业采购能力、发挥运营效率、节约采购成本、打破信息壁垒、开展实时监督等方面均能发挥巨大作用。它让采购过程更加公开、公平、公正，是企业实现"阳光采购、廉洁采购"的必备工具。'
      },
      {
        id: 5,
        title: '被装按需申领小程序',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/BeiZhuangShenLing.jpg',
        summary: '为公安等单位打造的个性化、数字化被装申领与管理平台。',
        background: '传统的被装发放模式多为"一刀切"的标配发放，存在尺码不合、库存积压、资源浪费等问题。如何实现按需申领、精准发放、科学管理，是现代后勤保障工作面临的挑战。',
        solution: '本系统通过"小程序+PC后台"的模式，实现了被装管理的全流程自动化：\n小程序端（用户侧）：民警可在线查看自己的被装额度，根据身高、体型和实际需求，自主选择所需服装的品种和尺码进行申领，并实时查看订单状态。\nPC管理端（管理侧）：管理员可进行预算决算、供应商管理、出入库管理、发放控制等操作。系统还能根据申领数据进行智能分析，为未来的采购计划提供决策依据。',
        value: '系统彻底改变了过去"人找衣服"的被动模式，实现了"衣服找人"的精准服务。它不仅提高了民警的满意度，还极大降低了仓储成本和管理成本，实现了后勤保障的降本增效。'
      },
      {
        id: 6,
        title: '国控粮油商城管理平台',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/XiTong/GuoKongLiangYou.png',
        summary: '为国控粮油打造的线上线下一体化经营的电商平台。',
        background: '随着移动互联网的发展，线上购物已成为主流。传统粮油企业面临着客户流失、销售渠道单一的挑战，迫切需要通过数字化转型，拓展线上渠道，为消费者提供更便捷的购物体验。',
        solution: '本平台是一套为粮油农产品企业量身打造的，集线上商城、线下收银、后台管理于一体的O2O新零售解决方案：\n线上同城零售（小程序）：用户可通过小程序浏览商品、在线下单，并选择快递配送或到店自提，享受便捷的购物体验。\n线下门店收银系统：为线下门店提供简单易用的收银系统，支持现金、扫码、储值等多种支付方式，并支持断网离线收银，确保经营不中断。\n强大的ERP后台：提供【下单-出库-退货-收款】全流程订单管理、自动生成应收/应付款表单、销售经营数据统计等强大功能，让业绩情况一目了然。\n门店一体化经营：线上小程序与线下收银系统数据实时同步，实现了商品、会员、订单、库存的完全统一，真正打通了线上线下。',
        value: '该平台助力传统粮油企业成功实现了"互联网+"转型。它不仅为企业开辟了新的线上销售渠道，更通过线上线下一体化，优化了客户体验，提升了运营效率，是传统零售企业数字化升级的优秀范本。'
      }
    ]
  }
})

// 门户网站建设案例数据
const websites = reactive<Record<WebsiteKey, WebsiteCategory>>({
  government: {
    title: '政府机关',
    description: '打造权威政府门户，构建阳光透明政务。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/zhengfu.jpeg',
    cases: [
      {
        id: 1,
        title: '上饶市信州区人民政府',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoRenMinZhenFu.jpg',
        summary: '为信州区人民政府精心打造的官方门户网站，是政府面向公众进行信息发布、提供在线服务、开展政民互动的重要窗口。',
        overview: '为信州区人民政府精心打造的官方门户网站，是政府面向公众进行信息发布、提供在线服务、开展政民互动的重要窗口。网站设计风格庄重、大气，栏目结构清晰，充分体现了政府网站的权威性和服务性。',
        design: '遵循"简洁、易用、规范"的原则，主色调采用"政务红"与"天空蓝"，营造出权威、开放的视觉感受。首页布局采用模块化设计，突出要闻、公告、服务入口等核心内容。',
        technology: '采用PC端+手机端自适应技术，确保在不同设备上均有良好的浏览体验。后台配备了强大且易用的内容管理系统（CMS），方便政府工作人员进行信息的实时更新与维护。',
        hasVisitButton: true
      },
      {
        id: 2,
        title: '广丰区人民政府',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangFengRenMinZhenFu.png',
        summary: '为广丰区人民政府倾力打造的官方门户网站，是集信息发布、解读回应、政民互动、政务服务于一体的权威平台。',
        overview: '为广丰区人民政府倾力打造的官方门户网站，是集信息发布、解读回应、政民互动、政务服务于一体的权威平台，旨在建设阳光、服务、高效的网上政府。',
        design: '网站设计突出"魅力广丰"的地域特色，将城市风光与政务信息有机结合。页面布局工整，逻辑严谨，导航清晰，确保公众能快速找到所需信息，体现了政府网站的专业性和服务性。',
        technology: '采用安全稳定的技术架构，保障政府信息的权威性和安全性。后台系统支持多部门、多用户协同管理，权限划分明确，满足了政府复杂的内部信息管理需求。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '万年县人民政府',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/WanNianRenMinZhengFu.png',
        summary: '为"世界稻作文化发源地"——万年县打造的官方门户网站，展示万年独特历史文化、生态旅游资源的重要窗口。',
        overview: '为"世界稻作文化发源地"——万年县打造的官方门户网站。网站在承担政务公开职能的同时，也作为展示万年独特历史文化、生态旅游资源的重要窗口。',
        design: '网站视觉设计巧妙地融入了稻穗、水墨等文化元素，既庄重又富有地方特色。功能上强化了"政民互动"和"网上办事"入口，将服务功能前置，方便企业和群众使用。',
        technology: '网站实现了对各类政策文件、规划计划的系统化归档和智能检索，提升了信息查找效率。同时，响应式设计确保了在各类设备上的无缝浏览体验。',
        hasVisitButton: true
      }
    ]
  },
  police: {
    title: '公安机关',
    description: '弘扬警察精神，打造高效警民之窗。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/gongan.jpeg',
    cases: [
      {
        id: 1,
        title: '上饶公安审计信息网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoGongAnXinXiWang.jpg',
        summary: '为上饶市公安局内部审计部门打造的专业工作平台，用于发布审计动态、共享审计法规、交流审计经验。',
        overview: '为上饶市公安局内部审计部门打造的专业工作平台。网站主要面向公安系统内部，用于发布审计动态、共享审计法规、交流审计经验、规范工作流程。',
        design: '界面设计简洁、严肃、专业，符合公安内网的规范要求。色彩以"警徽蓝"为主，栏目设置紧扣审计工作的实际需求，如"文件传输"、"制度规范"、"表格下载"等。',
        technology: '网站部署于公安内网，对安全性有极高要求。系统采用了严格的权限管理和日志记录功能，确保了内部信息的安全可控。',
        hasVisitButton: false,
        isIntranet: true
      },
      {
        id: 2,
        title: '三清山公安局官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/SanQinShanGongAn.jpg',
        summary: '为三清山公安局定制的官方网站，是全国景区警务网站的标杆，服务游客、保障景区安全的重要线上窗口。',
        overview: '为三清山公安局定制的官方网站，是全国景区警务网站的标杆。网站不仅是警务公开的平台，更是服务游客、保障景区安全的重要线上窗口。',
        design: '设计风格将"警务元素"与"三清山自然风光"完美融合，既体现了公安的威严，又不失景区的亲和力。特别设置了"旅游警示"、"失物招领"等特色便民栏目。',
        technology: '网站地图功能与景区导览相结合，为游客提供安全指引。同时，强大的后台系统支持节假日等高峰期的高并发访问，确保服务不中断。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '上饶市公安局刑事警察支队官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GongAnXinZhenZhiDui.jpg',
        summary: '为上饶市刑警支队量身定制的官方网站，是发布权威警讯、展示刑侦工作成果的窗口。',
        overview: '为上饶市刑警支队量身定制的官方网站。网站不仅是发布权威警讯、展示刑侦工作成果的窗口，更是震慑犯罪、宣传普法、弘扬正能量的重要平台。',
        design: '视觉风格硬朗、锐利，大量运用了写实照片和深色调背景，营造出刑侦工作的专业与威严氛围。栏目设置上突出"队伍建设"、"利剑行动"、"防范宣传"等特色内容。',
        technology: '网站对图片和视频内容展示进行了优化，能够高清、流畅地展现刑警风采和工作动态。后台系统操作流程清晰，便于民警快速发布和管理信息。',
        hasVisitButton: true
      },
      {
        id: 4,
        title: '横峰县公安信息网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/HengFengGongAn.jpg',
        summary: '为横峰县公安局打造的官方警务信息门户网站，是"平安横峰"建设的重要线上阵地。',
        overview: '为横峰县公安局打造的官方警务信息门户网站，是"平安横峰"建设的重要线上阵地。网站集警务动态、警方提示、网上办事、警民互动等功能于一体，旨在构建和谐警民关系，提升公众安全感。',
        design: '网站设计庄重、权威，以"藏蓝"为主色调，辅以金色点缀，突出公安机关的形象。首页布局清晰，将"新闻中心"、"信息公开"、"在线服务"等核心板块进行模块化展示，方便群众快速获取信息。',
        technology: '网站具备强大的信息检索功能和安全防护体系，确保了政府信息的准确发布和网站的安全稳定运行。响应式设计保证了在不同终端设备上的良好体验。',
        hasVisitButton: true
      },
      {
        id: 5,
        title: '上饶市广丰区公安局官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangFengGongAn.jpg',
        summary: '为广丰区公安局打造的官方门户网站，展示广丰公安新形象、新作为，服务辖区群众的重要线上渠道。',
        overview: '为广丰区公安局打造的官方门户网站，全面贯彻"对党忠诚、服务人民、执法公正、纪律严明"的总要求。网站是展示广丰公安新形象、新作为，服务辖区群众的重要线上渠道。',
        design: '网站设计大气、阳光，以党徽、警徽等元素为核心，突出"党建引领"的主题。页面布局和内容组织上，将"警务要闻"与"便民服务"并重，体现了管理与服务的统一。',
        technology: '网站采用了先进的CMS系统，支持模板化、组件化管理，未来可根据工作重心的变化灵活调整页面布局。同时，系统对移动端进行了深度优化，方便群众通过手机访问。',
        hasVisitButton: true
      },
      {
        id: 2,
        title: '三清山公安',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/SanQinShanGongAn.jpg',
        summary: '为三清山公安局定制的官方网站，是全国景区警务网站的标杆，服务游客、保障景区安全的重要线上窗口。',
        overview: '为三清山公安局定制的官方网站，是全国景区警务网站的标杆。网站不仅是警务公开的平台，更是服务游客、保障景区安全的重要线上窗口。',
        design: '设计风格将"警务元素"与"三清山自然风光"完美融合，既体现了公安的威严，又不失景区的亲和力。特别设置了"旅游警示"、"失物招领"等特色便民栏目。',
        technology: '网站地图功能与景区导览相结合，为游客提供安全指引。同时，强大的后台系统支持节假日等高峰期的高并发访问，确保服务不中断。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '上饶市公安局刑事警察支队',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GongAnXinZhenZhiDui.jpg',
        summary: '为上饶市刑警支队量身定制的官方网站，是发布权威警讯、展示刑侦工作成果、震慑犯罪的重要平台。',
        overview: '为上饶市刑警支队量身定制的官方网站。网站不仅是发布权威警讯、展示刑侦工作成果的窗口，更是震慑犯罪、宣传普法、弘扬正能量的重要平台。',
        design: '视觉风格硬朗、锐利，大量运用了写实照片和深色调背景，营造出刑侦工作的专业与威严氛围。栏目设置上突出"队伍建设"、"利剑行动"、"防范宣传"等特色内容。',
        technology: '网站对图片和视频内容展示进行了优化，能够高清、流畅地展现刑警风采和工作动态。后台系统操作流程清晰，便于民警快速发布和管理信息。',
        hasVisitButton: true
      },
      {
        id: 4,
        title: '横峰县公安信息网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/HengFengGongAn.jpg',
        summary: '为横峰县公安局打造的官方警务信息门户网站，是"平安横峰"建设的重要线上阵地。',
        overview: '为横峰县公安局打造的官方警务信息门户网站，是"平安横峰"建设的重要线上阵地。网站集警务动态、警方提示、网上办事、警民互动等功能于一体，旨在构建和谐警民关系，提升公众安全感。',
        design: '网站设计庄重、权威，以"藏蓝"为主色调，辅以金色点缀，突出公安机关的形象。首页布局清晰，将"新闻中心"、"信息公开"、"在线服务"等核心板块进行模块化展示，方便群众快速获取信息。',
        technology: '网站具备强大的信息检索功能和安全防护体系，确保了政府信息的准确发布和网站的安全稳定运行。响应式设计保证了在不同终端设备上的良好体验。',
        hasVisitButton: true
      },
      {
        id: 5,
        title: '广丰公安',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangFengGongAn.jpg',
        summary: '为广丰区公安局打造的官方门户网站，展示广丰公安新形象、新作为，服务辖区群众的重要线上渠道。',
        overview: '为广丰区公安局打造的官方门户网站，全面贯彻"对党忠诚、服务人民、执法公正、纪律严明"的总要求。网站是展示广丰公安新形象、新作为，服务辖区群众的重要线上渠道。',
        design: '网站设计大气、阳光，以党徽、警徽等元素为核心，突出"党建引领"的主题。页面布局和内容组织上，将"警务要闻"与"便民服务"并重，体现了管理与服务的统一。',
        technology: '网站采用了先进的CMS系统，支持模板化、组件化管理，未来可根据工作重心的变化灵活调整页面布局。同时，系统对移动端进行了深度优化，方便群众通过手机访问。',
        hasVisitButton: true
      }
    ]
  },
  enterprise: {
    title: '国企/集团',
    description: '塑造集团品牌形象，展示企业综合实力。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/guoqi.jpeg',
    cases: [
      {
        id: 1,
        title: '上饶国控投资集团有限公司',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuoKongTouZi.png',
        summary: '为上饶国控集团量身定制的官方门户网站，全面展示了国控集团作为城市建设主力军的雄厚实力和品牌形象。',
        overview: '为上饶国控集团量身定制的官方门户网站。网站整体设计现代、商务，通过对集团概况、新闻中心、主营业务、国控文化等板块的精心布局，全面展示了国控集团作为城市建设主力军的雄厚实力和品牌形象。',
        design: '采用宽屏、大图的设计风格，视觉上突出集团的实力与前瞻性。色彩上运用沉稳的商务色系，结合动态交互效果，提升网站的品质感和用户体验。',
        technology: '网站具备高度的可扩展性，能够适应集团未来业务发展的需要。同时，对新闻、招标等信息发布流程进行了优化，确保信息传递的及时性和准确性。',
        hasVisitButton: true
      },
      {
        id: 2,
        title: '上饶文旅集团官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWenLv.jpg',
        summary: '为上饶文旅集团量身定制的官方品牌门户网站，以"高铁枢纽，大美上饶"为主题，展示集团产业布局和辉煌成就。',
        overview: '为上饶文旅集团量身定制的官方品牌门户网站。网站以"高铁枢纽，大美上饶"为主题，全面展示了集团在旅游景区开发、文化项目投资、酒店运营管理等方面的产业布局和辉煌成就。',
        design: '网站采用全屏视频和大幅高清图片作为背景，给用户带来身临其境的沉浸式视觉体验。设计上将上饶的山水之美与集团的现代发展理念相结合，极具感染力。',
        technology: '网站对视频和高清图片资源进行了极致的加载优化，确保在提供震撼视觉效果的同时，不牺牲访问速度。后台系统支持对旗下各个文旅项目的独立内容管理。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '江西鑫邦实业集团官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoXingBang.jpg',
        summary: '为江西鑫邦实业集团打造的官方网站，全面展示集团在地产、建筑、贸易、酒店等多个领域的综合实力。',
        overview: '为江西鑫邦实业集团打造的官方网站。网站旨在全面展示集团在地产、建筑、贸易、酒店等多个领域的综合实力和企业文化，是集团品牌形象的重要线上名片。',
        design: '设计风格沉稳、大气、国际化，符合大型实业集团的定位。通过清晰的业务板块划分和富有视觉逻辑的页面布局，让访问者能快速了解集团庞大而多元的产业结构。',
        technology: '网站采用模块化开发，便于未来新增或调整业务板块。同时，对SEO（搜索引擎优化）进行了深度布局，提升了集团在网络上的品牌曝光度。',
        hasVisitButton: true
      },
      {
        id: 4,
        title: '际洲集团官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ZhouJiJiTuan.jpg',
        summary: '为际洲集团打造的新一代官方品牌网站，通过现代化的设计语言全面升级集团的线上品牌形象。',
        overview: '为际洲集团打造的新一代官方品牌网站。网站旨在通过现代化的设计语言和丰富的多媒体内容，全面升级集团的线上品牌形象，展示其在多个领域的业务成就和未来愿景。',
        design: '网站采用了极简主义的设计风格，通过大量的留白、精致的排版和高质量的实景图片，营造出高端、专业的品牌质感。交互上注重细节，鼠标悬停、页面滚动等都伴有细腻的动态效果。',
        technology: '网站前端应用了最新的Web技术，实现了流畅的动画效果和优秀的跨浏览器兼容性。内容管理系统支持视频、图集等多种媒体格式，使品牌故事的呈现更加生动、立体。',
        hasVisitButton: true
      },
      {
        id: 5,
        title: '上饶市医疗投资集团官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/YiLiaoTouZiJiTuan.jpg',
        summary: '为上饶市医疗投资集团构建的官方门户网站，以"服务民生，引领大健康产业"为核心。',
        overview: '为上饶市医疗投资集团构建的官方门户网站。网站以"服务民生，引领大健康产业"为核心，展示了集团在医疗项目投资、医院管理、康养产业等方面的战略布局和社会责任担当。',
        design: '网站设计兼具"医疗的专业严谨"与"投资的现代前瞻"。色彩上采用代表生命与健康的绿色和代表科技与信赖的蓝色，传递出集团的核心价值观。网站发起的"光盘行动"等公益活动，也通过专题形式进行了重点呈现。',
        technology: '网站信息架构清晰，将投资项目、新闻动态、企业文化、社会责任等内容进行了系统化梳理。强大的后台功能支持对复杂的项目信息进行高效管理和发布。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '鑫邦集团',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoXingBang.jpg',
        summary: '为江西鑫邦实业集团打造的官方网站，全面展示集团在地产、建筑、贸易、酒店等多个领域的综合实力和企业文化。',
        overview: '为江西鑫邦实业集团打造的官方网站。网站旨在全面展示集团在地产、建筑、贸易、酒店等多个领域的综合实力和企业文化，是集团品牌形象的重要线上名片。',
        design: '设计风格沉稳、大气、国际化，符合大型实业集团的定位。通过清晰的业务板块划分和富有视觉逻辑的页面布局，让访问者能快速了解集团庞大而多元的产业结构。',
        technology: '网站采用模块化开发，便于未来新增或调整业务板块。同时，对SEO（搜索引擎优化）进行了深度布局，提升了集团在网络上的品牌曝光度。',
        hasVisitButton: true
      },
      {
        id: 4,
        title: '际洲集团',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ZhouJiJiTuan.jpg',
        summary: '为际洲集团打造的新一代官方品牌网站，通过现代化的设计语言和丰富的多媒体内容，全面升级集团的线上品牌形象。',
        overview: '为际洲集团打造的新一代官方品牌网站。网站旨在通过现代化的设计语言和丰富的多媒体内容，全面升级集团的线上品牌形象，展示其在多个领域的业务成就和未来愿景。',
        design: '网站采用了极简主义的设计风格，通过大量的留白、精致的排版和高质量的实景图片，营造出高端、专业的品牌质感。交互上注重细节，鼠标悬停、页面滚动等都伴有细腻的动态效果。',
        technology: '网站前端应用了最新的Web技术，实现了流畅的动画效果和优秀的跨浏览器兼容性。内容管理系统支持视频、图集等多种媒体格式，使品牌故事的呈现更加生动、立体。',
        hasVisitButton: true
      },
      {
        id: 5,
        title: '上饶市医疗投资集团',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/YiLiaoTouZiJiTuan.jpg',
        summary: '为上饶市医疗投资集团构建的官方门户网站，以"服务民生，引领大健康产业"为核心，展示集团的战略布局和社会责任担当。',
        overview: '为上饶市医疗投资集团构建的官方门户网站。网站以"服务民生，引领大健康产业"为核心，展示了集团在医疗项目投资、医院管理、康养产业等方面的战略布局和社会责任担当。',
        design: '网站设计兼具"医疗的专业严谨"与"投资的现代前瞻"。色彩上采用代表生命与健康的绿色和代表科技与信赖的蓝色，传递出集团的核心价值观。网站发起的"光盘行动"等公益活动，也通过专题形式进行了重点呈现。',
        technology: '网站信息架构清晰，将投资项目、新闻动态、企业文化、社会责任等内容进行了系统化梳理。强大的后台功能支持对复杂的项目信息进行高效管理和发布。',
        hasVisitButton: true
      }
    ]
  },
  institution: {
    title: '企事业单位',
    description: '定制专业门户网站，传递单位核心价值。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/shiyedanwei.jpeg',
    cases: [
      {
        id: 1,
        title: '上饶市广信区市政公用服务中心官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangXinQuShiZhengGongYongFuWu.jpg',
        summary: '为广信区市政公用服务中心打造的官方信息门户，以服务市民为核心，展现环境卫生、园林绿化、市政工程等服务成果。',
        overview: '为广信区市政公用服务中心打造的官方信息门户。网站以服务市民为核心，集新闻资讯、信息公示、中心动态、工作文化展示于一体，全面展现了中心在环境卫生、园林绿化、市政工程等方面的服务成果与专业形象。',
        design: '网站主色调采用清新的"环保绿"与"天空蓝"，象征着绿色、洁净的城市环境。布局清晰，将市民最关心的"通知公告"和"工作动态"置于首页核心位置，方便快速查阅。整体风格亲民、务实。',
        technology: '采用成熟稳定的网站架构，确保7x24小时不间断访问。后台内容管理系统操作便捷，支持图文、视频等多种信息格式的发布，使中心工作人员可以轻松维护网站内容，保证信息传递的及时性。',
        hasVisitButton: true
      },
      {
        id: 2,
        title: '广信工投集团官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/GuangXinGongTouJiTuan.jpg',
        summary: '为广信工投集团精心打造的官方品牌网站，全面展示集团在工业项目投资、资产管理、园区开发等方面的核心业务。',
        overview: '为广信工投集团精心打造的官方品牌网站。网站全面展示了集团在工业项目投资、资产管理、园区开发等方面的核心业务与战略布局，是集团对外展示实力、寻求合作、树立行业标杆的重要平台。',
        design: '网站设计风格现代、稳健，运用富有冲击力的工业主题视觉元素，体现了工业投资领域的专业与力量感。导航结构逻辑清晰，引导用户快速了解集团的业务板块和发展动态。',
        technology: '网站在前端应用了多种交互特效，提升了页面的现代感和浏览趣味性。同时，网站架构具有良好的兼容性和安全性，为集团的品牌形象提供了坚实的技术保障。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '上饶市文化创意产业专题网站',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWenHuanChuangYi.jpg',
        summary: '为庆祝建党百年，展示上饶文化创意成果而特别策划的专题网站，以"红色文化"为核心主题。',
        overview: '为庆祝建党百年，展示上饶文化创意成果而特别策划的专题网站。网站以"红色文化"为核心，通过丰富的视觉元素和互动形式，生动呈现了主题内容。',
        design: '网站采用极具视觉冲击力的"中国红"作为主色调，运用了大量的设计感图标和动态效果，打破了传统专题网站的沉闷感，使主题宣传更具吸引力和感染力。',
        technology: '采用H5技术，实现了丰富的页面动效和跨平台兼容性。页面为单页滚动式设计，信息流清晰，引导用户一览到底，非常适合专题内容的呈现。',
        hasVisitButton: true
      },
      {
        id: 4,
        title: '"唱支山歌给党听"群众歌咏活动专题网站',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ChangZhiShanGeGeiDangTing.jpg',
        summary: '为庆祝建党百年大型群众歌咏活动而特别制作的官方专题网站，集活动介绍、在线报名、作品展播、网络投票等功能于一体。',
        overview: '为庆祝建党百年大型群众歌咏活动而特别制作的官方专题网站。网站集活动介绍、在线报名、作品展播、网络投票等功能于一体，是整个活动的线上主阵地。',
        design: '网站整体设计喜庆、热烈，以红色和金色为主色调，运用了音符、话筒等音乐元素，营造出浓厚的活动氛围。页面结构围绕"看、听、评、传"四个核心体验展开，引导用户深度参与。',
        technology: '网站的核心是视频展播和在线投票系统。我们为之开发了稳定、高效的视频播放和防刷票机制，确保了活动的顺利进行和结果的公平公正。',
        hasVisitButton: true
      },
      {
        id: 5,
        title: '上饶市科学技术协会官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoKeXie.jpg',
        summary: '为上饶市科协打造的官方网站，是党和政府联系科技工作者的桥梁和纽带，旨在弘扬科学精神、普及科学知识。',
        overview: '为上饶市科协打造的官方网站，是党和政府联系科技工作者的桥梁和纽带。网站旨在弘扬科学精神、普及科学知识、服务科技工作者、促进学术交流。',
        design: '网站设计风格现代、简约、富有科技感。主色调采用"科技蓝"，页面中融入了齿轮、芯片、分子结构等科技元素。栏目规划上突出"科学普及"、"学会动态"、"建言献策"等科协核心职能。',
        technology: '网站建立了完善的会员系统和线上投稿系统，方便科技工作者在线交流和提交学术成果。同时，网站与科普中国的资源库进行了对接，极大地丰富了科普内容。',
        hasVisitButton: true
      }
    ]
  },
  hospital: {
    title: '医院/学校',
    description: '服务患者与师生，打造温情信息平台。',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/chanpinfengmiantu/yiyuan.jpeg',
    cases: [
      {
        id: 1,
        title: '上饶市人民医院官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoRenMinYiYuan.jpg',
        summary: '为三级甲等综合医院——上饶市人民医院构建的官方服务平台，为患者提供权威、便捷、全面的线上医疗信息服务。',
        overview: '为三级甲等综合医院——上饶市人民医院构建的官方服务平台。网站集医院概况、科室介绍、专家风采、就医指南、健康科普于一体，旨在为患者提供权威、便捷、全面的线上医疗信息服务。',
        design: '采用温暖、洁净的色调，营造出专业、可信赖的医疗氛围。首页将"科室导航"、"专家介绍"、"预约挂号"等患者核心需求功能置于最显眼的位置，极大优化了患者的线上就医体验。',
        technology: '网站与医院内部的HIS系统进行了深度对接（或预留接口），为未来实现在线预约、报告查询等深度服务打下基础。网站后台管理系统功能强大，支持各科室独立维护本科室的专家信息和科普文章。',
        hasVisitButton: true
      },
      {
        id: 2,
        title: '上饶市第三人民医院官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoDiSanYiYuan.jpg',
        summary: '为上饶市第三人民医院（市精神卫生中心）打造的官方服务网站，特别注重对心理健康知识的科普和对患者隐私的保护。',
        overview: '为上饶市第三人民医院（市精神卫生中心）打造的官方服务网站。网站在提供常规医疗信息的同时，特别注重对心理健康知识的科普和对患者隐私的保护。',
        design: '网站色调采用柔和、温暖的蓝色系，营造出安宁、治愈的氛围。设计上突出"专家团队"和"心理咨询"板块，为患者建立信任感和寻求帮助的便捷通道。',
        technology: '网站内置了加密的在线咨询表单功能，确保患者信息的私密性。同时，内容管理系统支持医生团队便捷地发布科普文章和视频，打造专业的心理健康知识库。',
        hasVisitButton: true
      },
      {
        id: 3,
        title: '上饶卫校附属医院（广丰区妇幼保健院）官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWeiXiaoFuShuYiYuan.jpg',
        summary: '为上饶卫校附属医院（广丰区妇幼保健院）打造的官方服务网站，以"关爱妇幼，相约健康"为宗旨。',
        overview: '为上饶卫校附属医院（广丰区妇幼保健院）打造的官方服务网站。网站以"关爱妇幼，相约健康"为宗旨，为广大妇女儿童提供线上健康科普、科室导航、专家介绍等一站式信息服务。',
        design: '网站设计温馨、亲切，采用粉色、浅蓝等柔和色调，营造出充满关爱的氛围。功能上特别强化了"孕妇学校"、"儿童保健"、"产后康复"等特色专科的内容展示，方便目标人群查找。',
        technology: '网站建立了完善的专家库和知识库系统，支持按科室、按疾病进行内容分类和检索。后台系统易于操作，便于医护人员自主更新科普知识和专家出诊信息。',
        hasVisitButton: true
      },
      {
        id: 4,
        title: '上饶中学官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoZhongXue.jpg',
        summary: '为知名学府——上饶中学量身打造的官方门户网站，是学校对外展示办学理念、校园文化、师资力量和教学成果的核心窗口。',
        overview: '为知名学府——上饶中学量身打造的官方门户网站。网站是学校对外展示办学理念、校园文化、师资力量和教学成果的核心窗口，也是服务在校师生、连接广大家长与校友的重要纽带。',
        design: '网站设计充满青春与学术气息，以"上中绿"为主色调，结合丰富的校园活动照片与视频，生动展现了学校的活力与风采。栏目设置紧扣"校园动态"、"德育教育"、"招生专栏"等核心主题，信息架构清晰合理。',
        technology: '网站采用了响应式布局，完美适配电脑、平板、手机等多种设备，方便师生和家长随时随地访问。强大的后台系统支持发布图文、视频、文件下载等多种形式的内容，满足了学校多样化的信息发布需求。',
        hasVisitButton: true
      },
      {
        id: 5,
        title: '上饶卫生学校官网',
        image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/WangZhanAiLi/ShangRaoWeiShengXueXiao.png',
        summary: '为培养医护人才的摇篮——上饶卫生学校建设的官方门户网站，核心服务于在校师生、意向考生及用人单位。',
        overview: '为培养医护人才的摇篮——上饶卫生学校建设的官方门户网站。网站核心服务于在校师生、意向考生及用人单位，是学校教学成果、招生信息、就业服务的重要发布平台。',
        design: '网站设计风格融合了"教育的严谨"与"医学的圣洁"，界面干净、专业。首页突出"招生专栏"、"专业介绍"、"校园招聘"等核心功能，目标导向明确。',
        technology: '网站具有强大的信息发布和管理功能，能够系统化地展示学校的专业设置、课程体系、实训基地等内容。响应式设计确保了学生和家长在手机端也能获得流畅的访问体验。',
        hasVisitButton: true
      }
    ]
  }
})

// 生命周期钩子
onMounted(() => {
  checkMobile()
  // 手机端默认显示门户网站建设案例
  if (isMobile.value) {
    activeTab.value = 'websites'
  }
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>{{ isMobile ? '门户网站建设案例' : '产品中心' }}</h1>
    </div>

    <div class="content">
      <!-- Tab切换 -->
      <div class="tab-container" v-if="!isMobile">
        <div class="tab-buttons">
          <button
            :class="['tab-btn', { active: activeTab === 'solutions' }]"
            @click="activeTab = 'solutions'"
          >
            智慧解决方案
          </button>
          <button
            :class="['tab-btn', { active: activeTab === 'websites' }]"
            @click="activeTab = 'websites'"
          >
            门户网站建设案例
          </button>
        </div>
      </div>

      <!-- 智慧解决方案内容 - 手机端不显示 -->
      <div v-if="activeTab === 'solutions' && !isMobile" class="solutions-content">
        <!-- 如果没有选中分类，显示分类卡片 -->
        <div v-if="!activeSolutionSection" class="cards-grid">
          <div
            v-for="(solution, key) in solutions"
            :key="key"
            class="card-item"
            @click="toggleSolutionSection(key as SectionKey)"
          >
            <div class="card-image">
              <img :src="solution.image" :alt="solution.title" />
            </div>
            <div class="card-content">
              <h3>{{ solution.title }}</h3>
              <p>{{ solution.description }}</p>
            </div>
          </div>
        </div>

        <!-- 如果选中了分类，显示该分类下的案例 -->
        <div v-else>
          <div class="section-header">
            <button @click="activeSolutionSection = null" class="back-btn">
              ← 返回
            </button>
            <h2>{{ solutions[activeSolutionSection].title }}</h2>
          </div>
          <div class="cards-grid">
            <div
              v-for="case_ in solutions[activeSolutionSection].cases"
              :key="case_.id"
              class="card-item"
              @click="showCaseDetail(case_)"
            >
              <div class="card-image">
                <img :src="case_.image" :alt="case_.title" />
              </div>
              <div class="card-content">
                <h3>{{ case_.title }}</h3>
                <p>{{ case_.summary }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 门户网站建设案例内容 -->
      <div v-if="activeTab === 'websites' || isMobile" class="websites-content">
        <!-- 如果没有选中分类，显示分类卡片 -->
        <div v-if="!activeWebsiteSection" class="cards-grid">
          <div
            v-for="(category, key) in websites"
            :key="key"
            class="card-item"
            @click="toggleWebsiteSection(key as WebsiteKey)"
          >
            <div class="card-image">
              <img :src="category.image" :alt="category.title" />
            </div>
            <div class="card-content">
              <h3>{{ category.title }}</h3>
              <p>{{ category.description }}</p>
            </div>
          </div>
        </div>

        <!-- 如果选中了分类，显示该分类下的案例 -->
        <div v-else>
          <div class="section-header">
            <button @click="activeWebsiteSection = null" class="back-btn">
              ← 返回
            </button>
            <h2>{{ websites[activeWebsiteSection].title }}</h2>
          </div>
          <div class="cards-grid">
            <div
              v-for="website in websites[activeWebsiteSection].cases"
              :key="website.id"
              class="card-item"
              @click="showWebsiteDetail(website)"
            >
              <div class="card-image">
                <img :src="website.image" :alt="website.title" />
              </div>
              <div class="card-content">
                <h3>{{ website.title }}</h3>
                <p>{{ website.summary }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>

    <!-- 案例详情弹窗 -->
    <div v-if="dialogVisible" class="dialog-overlay" @click="closeDialog">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h2>{{ currentCase?.title }}</h2>
          <button class="close-btn" @click="closeDialog">
            <el-icon><Close /></el-icon>
          </button>
        </div>
        <div class="dialog-body">
          <div class="case-detail-image">
            <img :src="currentCase?.image" :alt="currentCase?.title" />
          </div>
          <div class="case-detail-content">
            <!-- 智慧解决方案案例显示 -->
            <template v-if="currentCase?.background">
              <div class="detail-section">
                <h3>项目背景</h3>
                <p>{{ currentCase?.background }}</p>
              </div>
              <div class="detail-section">
                <h3>解决方案</h3>
                <p v-for="(line, index) in currentCase?.solution?.split('\n')" :key="index">
                  {{ line }}
                </p>
              </div>
              <div class="detail-section">
                <h3>平台价值</h3>
                <p>{{ currentCase?.value }}</p>
              </div>
            </template>

            <!-- 门户网站案例显示 -->
            <template v-else-if="currentCase?.overview">
              <div class="detail-section">
                <h3>项目概述</h3>
                <p>{{ currentCase?.overview }}</p>
              </div>
              <div class="detail-section">
                <h3>设计理念与技术实现</h3>
                <div class="subsection">
                  <h4>设计理念</h4>
                  <p>{{ currentCase?.design }}</p>
                </div>
                <div class="subsection">
                  <h4>技术实现</h4>
                  <p>{{ currentCase?.technology }}</p>
                </div>
              </div>
              <div v-if="currentCase?.hasVisitButton" class="detail-section">
                <h3>在线访问</h3>
                <button class="visit-btn" @click="visitWebsite">
                  <el-icon><ArrowRight /></el-icon>
                  点击访问网站
                </button>
              </div>
              <div v-else-if="currentCase?.isIntranet" class="detail-section">
                <h3>访问说明</h3>
                <div class="intranet-notice">
                  <el-icon><Setting /></el-icon>
                  内网访问，仅供效果展示
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
}

.header {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Tab切换样式 */
.tab-container {
  margin-bottom: 1.5rem;
}

.tab-buttons {
  display: flex;
  background: white;
  border-radius: 0.75rem;
  padding: 0.25rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.tab-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: #666;
  font-weight: 500;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tab-btn.active {
  background: linear-gradient(135deg, #1693d2, #3b82f6);
  color: white;
  box-shadow: 0 2px 8px rgba(22, 147, 210, 0.3);
}

.tab-btn:hover:not(.active) {
  background: #f0f9ff;
  color: #1693d2;
}

/* 解决方案内容样式 */
.solutions-content {
  padding: 1rem;
}

/* 解决方案卡片网格 */
.solution-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.solution-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.solution-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
}

.solution-image {
  height: 180px;
  overflow: hidden;
}

.solution-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.solution-card:hover .solution-image img {
  transform: scale(1.05);
}

.solution-content {
  padding: 1.25rem;
}

.solution-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1693d2;
}

.solution-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 网站卡片网格 */
.website-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.website-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.website-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
}

.website-image {
  height: 180px;
  overflow: hidden;
}

.website-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.website-card:hover .website-image img {
  transform: scale(1.05);
}

.website-content {
  padding: 1.25rem;
}

.website-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1693d2;
}

.website-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.solution-section {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.section-header {
  padding: 1.25rem 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.section-header:hover {
  background: #f0f9ff;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.section-icon {
  font-size: 1.3rem;
  color: #1693d2;
}

.expand-icon {
  font-size: 1.2rem;
  color: #666;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.section-description {
  padding: 0 1.5rem 1.25rem 1.5rem;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 案例网格样式 */
.cases-grid {
  padding: 0 1.5rem 1.5rem 1.5rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.case-card {
  background: #f8fafc;
  border-radius: 0.75rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
  border-color: #1693d2;
}

.case-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.case-card:hover .case-image img {
  transform: scale(1.05);
}

.case-content {
  padding: 1.25rem;
}

.case-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.case-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

.case-card-placeholder {
  padding: 2rem;
  text-align: center;
  color: #999;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 2px dashed #e5e7eb;
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.dialog-content {
  background: white;
  border-radius: 1rem;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafbfc;
}

.dialog-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  color: #666;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.dialog-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.case-detail-image {
  margin-bottom: 1.5rem;
}

.case-detail-image img {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.case-detail-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-section h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1693d2;
  border-left: 4px solid #1693d2;
  padding-left: 0.75rem;
}

.detail-section p {
  margin: 0 0 0.5rem 0;
  color: #555;
  line-height: 1.6;
}

/* 网站案例内容样式 */
.websites-content {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.websites-content .placeholder h3 {
  color: #1693d2;
  margin-bottom: 1rem;
}

.websites-content .placeholder p {
  color: #666;
}

/* 门户网站建设案例样式 */
.websites-content {
  padding: 1rem 0;
}

.website-category {
  margin-bottom: 3rem;
}

.category-header {
  margin-bottom: 2rem;
  text-align: center;
}

.category-header h2 {
  color: #1693d2;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.category-description {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
  max-width: 800px;
  margin: 0 auto;
}

.websites-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.website-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.website-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(22, 147, 210, 0.15);
}

.website-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.website-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.website-card:hover .website-image img {
  transform: scale(1.05);
}

.website-content {
  padding: 1.5rem;
}

.website-content h3 {
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.website-content p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.intranet-tag {
  background: #f0f9ff;
  color: #1693d2;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  margin-top: 0.75rem;
  display: inline-block;
  border: 1px solid #e0f2fe;
}

/* 弹窗中的门户网站样式 */
.subsection {
  margin-bottom: 1.5rem;
}

.subsection h4 {
  color: #1693d2;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.visit-btn {
  background: #1693d2;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.visit-btn:hover {
  background: #1472a8;
  transform: translateY(-1px);
}

.intranet-notice {
  background: #f8f9fa;
  color: #666;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .cases-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .tab-btn {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }

  .section-header {
    padding: 1.5rem 2rem;
  }

  .section-description {
    padding: 0 2rem 1.5rem 2rem;
  }

  .cases-grid {
    padding: 0 2rem 2rem 2rem;
  }

  .websites-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .website-image {
    height: 250px;
  }
}

@media (min-width: 1024px) {
  .cases-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .websites-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .website-image {
    height: 280px;
  }

  .dialog-content {
    max-width: 900px;
  }
}

@media (max-width: 767px) {
  .dialog-overlay {
    padding: 0.5rem;
  }

  .dialog-header {
    padding: 1rem;
  }

  .dialog-body {
    padding: 1rem;
  }

  .dialog-header h2 {
    font-size: 1.1rem;
  }
}

/* 网站类型Tab标签样式 */
.website-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.website-tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 2rem;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.website-tab-btn:hover {
  border-color: #1693d2;
  color: #1693d2;
  transform: translateY(-2px);
}

.website-tab-btn.active {
  background: #1693d2;
  border-color: #1693d2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(22, 147, 210, 0.3);
}

/* 当前分类描述样式 */
.current-category-description {
  text-align: center;
  margin-bottom: 2rem;
}

.current-category-description p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* 瀑布流卡片布局样式 */
.waterfall-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.waterfall-card {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.waterfall-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.waterfall-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(22, 147, 210, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.waterfall-card:hover .card-overlay {
  opacity: 1;
}

.overlay-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-weight: 500;
}

.card-content {
  padding: 1.5rem;
}

.card-content h3 {
  color: #1693d2;
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  line-height: 1.3;
}

.card-content p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.intranet-badge,
.online-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
}

.intranet-badge {
  background: #f0f0f0;
  color: #666;
}

.online-badge {
  background: #e8f5e8;
  color: #4caf50;
}

.intranet-overlay {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
}

/* 弹窗内访问按钮样式 */
.visit-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #1693d2;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.visit-btn:hover {
  background: #0f7bb8;
  transform: translateY(-2px);
}

.intranet-notice {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #f5f5f5;
  color: #666;
  border-radius: 0.5rem;
  font-size: 0.9rem;
}

/* 弹窗内子标题样式 */
.subsection {
  margin-bottom: 1.5rem;
}

.subsection h4 {
  color: #1693d2;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.subsection p {
  color: #666;
  line-height: 1.6;
}

/* 解决方案详情和网站详情 */
.solution-detail, .website-detail {
  background: white;
  border-radius: 1rem;
  margin-top: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.solution-detail-header, .website-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.solution-detail-header h2, .website-detail-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #1693d2;
}

/* 统一卡片列表 */
.cards-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
}

.card-item {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(22, 147, 210, 0.15);
}

.card-image {
  width: 120px;
  height: 120px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-item:hover .card-image img {
  transform: scale(1.05);
}

.card-content {
  padding: 1.25rem;
  flex: 1;
}

.card-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1693d2;
}

.card-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 分类标题样式 */
.section-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  margin-bottom: 1rem;
  position: relative;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1693d2;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.section-header .back-btn {
  background: #1693d2;
  border: 1px solid #1693d2;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  margin-right: 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.section-header .back-btn:hover {
  background-color: #0f7bb8;
  border-color: #0f7bb8;
}
</style>
