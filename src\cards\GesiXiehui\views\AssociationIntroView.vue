<template>
  <div class="page">
    <!-- 顶部导航 -->
    <div class="header">
      <button @click="goBack" class="back-btn">‹ 返回</button>
      <h1>协会介绍</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 欢迎页 -->
      <div class="section">
        <div class="welcome-card">
          <h1>上饶市民营（个私）经济协会</h1>
          <p>AI应用的实践者与赋能者</p>
        </div>
      </div>

      <!-- 关于我们 -->
      <div class="section">
        <div class="card">
          <h2>关于我们</h2>
          <p>上饶市民营（个私）经济协会是江西省规模领先的民营经济服务平台，年产值交易额超1500亿元。协会以"政治健全、党建促会、服务兴会"为宗旨，打造"企业生命周期一站式服务中心"，推动全市个私经济协同发展。</p>
        </div>
      </div>

      <!-- 协会发展历程 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Clock /></el-icon>
            协会发展历程
          </h2>
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-date">1987.10</div>
              <div class="timeline-content">成立江西省个体劳动者协会上饶地区工作委员会</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">1990.5</div>
              <div class="timeline-content">改组为江西省个体私营经济协会上饶地区工作委员会</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2001.11</div>
              <div class="timeline-content">正式成立上饶市个体私营经济协会</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2017.11</div>
              <div class="timeline-content">丁永红任办公室主任</div>
            </div>
            <div class="timeline-item">
              <div class="timeline-date">2022.11</div>
              <div class="timeline-content">傅利平当选会长</div>
            </div>
            <div class="timeline-item highlight">
              <div class="timeline-date">2023.3</div>
              <div class="timeline-content">更名为上饶市民营（个私）经济协会</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频 -->
      <div class="section">
        <div class="card">
          <h2>协会介绍视频</h2>
          <video controls style="width: 100%; max-width: 600px; border-radius: 8px;">
            <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/GeSiXieHui/xcsp.mp4" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>

      <!-- 六大服务中心 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Grid /></el-icon>
            六大服务中心
          </h2>
          <div class="service-grid">
            <div class="service-item">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E9%87%91%E8%9E%8D%E6%9C%8D%E5%8A%A1.jpg" alt="金融服务中心" />
              <h3>
                <el-icon><Money /></el-icon>
                金融服务中心
              </h3>
            </div>
            <div class="service-item">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E6%B3%95%E5%8A%A1%E6%9C%8D%E5%8A%A1.jpg" alt="法务服务中心" />
              <h3>
                <el-icon><Management /></el-icon>
                法务服务中心
              </h3>
            </div>
            <div class="service-item">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E7%A8%8E%E5%8A%A1%E6%9C%8D%E5%8A%A1.jpg" alt="财税服务中心" />
              <h3>
                <el-icon><Document /></el-icon>
                财税服务中心
              </h3>
            </div>
            <div class="service-item">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E7%9F%A5%E8%AF%86%E4%BA%A7%E6%9D%83%E5%93%81%E7%89%8C.jpg" alt="知识产权品牌中心" />
              <h3>
                <el-icon><Trophy /></el-icon>
                知识产权品牌中心
              </h3>
            </div>
            <div class="service-item">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E4%BA%BA%E6%89%8D.jpg" alt="人才服务中心" />
              <h3>
                <el-icon><User /></el-icon>
                人才服务中心
              </h3>
            </div>
            <div class="service-item">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%9F%B9%E8%AE%AD%E6%9C%8D%E5%8A%A1%E4%B8%AD%E5%BF%83.jpg" alt="培训服务中心" />
              <h3>
                <el-icon><Reading /></el-icon>
                培训服务中心
              </h3>
            </div>
          </div>
        </div>
      </div>


      <!-- 最新活动 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Star /></el-icon>
            最新活动
          </h2>

          <!-- 一码通上饶活动 -->
          <div class="activity-item">
            <h3>"一码通上饶"商家入驻活动持续升温，共建数字消费新生态</h3>
            <p class="activity-intro">上饶市民营（个私）经济协会正积极推动"一码通上饶"项目，旨在通过数字化赋能，构建更活跃的消费生态圈。自4月13日成功举办首场商家入驻邀请会以来，协会于4月18日下午又成功举办了第二场商家入驻邀约会，显示出该项目在当地的强劲势头和广泛吸引力。</p>

            <div class="activity-section">
              <h4>首场会议：奠定合作基础</h4>
              <p>4月13日的首场邀请会，由市民个协会长傅利平、秘书长徐如胜及首批意向商家共同出席。徐如胜秘书长通过PPT路演，全面系统地介绍了"一码通上饶"项目的实施方案、核心内容与创新亮点。会议互动热烈，商家代表们积极探讨项目细节。傅利平会长在总结时强调，希望各行业商家深化合作，形成合力，共同推动项目落地见效。此次会议吸引了体育中心羽毛球馆、饶商国际酒店、乐在自家人餐饮、卡拉多蛋糕、广信传说等多家知名企业参与，涵盖文体、酒店、餐饮、零售等多个领域，为项目初期奠定了良好基础。</p>
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/HuiYi.png" alt="首场会议现场" class="activity-image" />
            </div>

            <div class="activity-section">
              <h4>第二场会议：深化拓展合作领域</h4>
              <p>4月18日的第二场邀约会延续了首场的成功势头，吸引了近20家本地知名企业参与，涉及餐饮、零售、文旅、生活服务等更广泛的行业，包括老字号品牌金得利蛋糕、知名餐饮乡下菜，以及奥水体育、带湖路职工健身中心等生活服务业代表。会议伊始，徐如胜秘书长介绍了"一码通上饶"平台的背景、目标及其对地方经济的积极意义，并组织商家进行自我介绍以增进了解。随后，他再次通过PPT路演，详细展示了平台的核心功能、运营模式及入驻优势，帮助商家全面理解数字化赋能的价值。</p>
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/HuiYi22.webp" alt="第二场会议现场" class="activity-image" />
            </div>

            <div class="activity-section">
              <p>在互动答疑环节，与会商家围绕入驻流程、条件、优惠要求及平台推广等核心问题踊跃提问。傅利平会长针对商家准入条件、优惠力度、优惠方式、可视性与操作性，以及对入驻商品的宣传推广措施等方面进行了全面解答。他鼓励商家积极投身其中，共同为助力上饶消费、普惠广大消费者、促进上饶民营经济发展贡献力量。</p>
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/HuiZhang.png" alt="会长发言" class="activity-image" />
              <p class="activity-conclusion">这两场活动的成功举办，充分展现了"一码通上饶"项目在商家中的高度认可和积极响应，预示着一个更加便捷、普惠的数字消费生态正在上饶加速形成。</p>
            </div>
          </div>

          <!-- 信州春大舞台活动 -->
          <div class="activity-item">
            <h3>信州春大舞台：一个实现梦想与经济发展的平台</h3>
            <p class="activity-intro">信州春大舞台，由上饶民营企业家协会傅立平会长，同时也是信州春集团董事长投入数月时间筹备，旨在打造一个多功能平台。该舞台的目标是实现多重目标，助力上饶的夜经济发展，推广当地特色产品，并让更多有才艺的人实现梦想。</p>

            <div class="stage-goals">
              <h4>信州春大舞台的核心目标包括：</h4>

              <div class="goal-item">
                <h5>打造夜经济新亮点</h5>
                <p>大舞台将成为推动上饶夜间经济的重要载体，旨在将上饶的秀美山水、特色物产和优质产品推向全国乃至全球，吸引更多游客。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/YeShengHuo.jpg" alt="夜经济发展" class="activity-image" />
              </div>

              <div class="goal-item">
                <h5>拓宽企业销售渠道</h5>
                <p>希望利用大舞台的公益性质，为协会内拥有优质产品的企业提供展示和推广的平台，拓宽他们的销售渠道，实现互利共赢。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/tu.jpg" alt="企业展示" class="activity-image" />
              </div>

              <div class="goal-item">
                <h5>助力信州春酒涅槃重生</h5>
                <p>大舞台也将成为信州春酒品牌重塑的舞台，目标是让信州春酒走进千家万户，为人们带去喜乐。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/Jiu.png" alt="信州春酒" class="activity-image" />
              </div>

              <div class="goal-item">
                <h5>赋能人才，孵化网红</h5>
                <p>信州春大舞台致力于成为上饶有才艺人士展现自我的梦想舞台。通过培育更多网红，将其转化为上饶经济发展的"长虹"，将流量转化为吸引客户的"存量"，共同推动上饶经济的繁荣。</p>
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/YeTu.jpg" alt="人才培育" class="activity-image" />
              </div>
            </div>

            <p class="activity-conclusion">信州春大舞台期待吸引全球的目光，欢迎四方宾客来到上饶，共同感受"爱美上饶欢迎你，来了就是一家人"的热情与魅力。</p>
          </div>
        </div>
      </div>

      <!-- 入会指南 -->
      <div class="section">
        <div class="card">
          <h2>入会指南</h2>
          <div class="membership-compact">
            <div class="membership-item">
              <h3>📋 入会条件</h3>
              <p>合法经营，法人无犯罪记录</p>
            </div>
            <div class="membership-item">
              <h3>💰 会费标准</h3>
              <div class="fees-compact">
                <div class="fee-row">
                  <span class="fee-label">会员单位</span>
                  <span class="fee-value">365元/年</span>
                </div>
                <div class="fee-row">
                  <span class="fee-label">理事单位</span>
                  <span class="fee-value">1000元/年</span>
                </div>
                <div class="fee-row">
                  <span class="fee-label">副会长单位</span>
                  <span class="fee-value">3000元/年</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


     
       

      <!-- 联系我们 -->
      <div class="section">
        <div class="card">
          <h2>联系我们 / 开启合作</h2>
          <div class="contact-simple">
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <span class="contact-simple-icon">📍</span>
                <span>地址</span>
              </div>
              <div class="contact-simple-value">江西省上饶市信州区常湖路60号</div>
            </div>
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <span class="contact-simple-icon">📞</span>
                <span>电话</span>
              </div>
              <div class="contact-simple-value">
                <span class="phone-number">188 2772 6669</span>
                <div class="contact-simple-actions">
                  <button class="btn-simple" @click="copyPhone">复制</button>
                  <button class="btn-simple" @click="callPhone">拨打</button>
                </div>
              </div>
            </div>
            <div class="contact-simple-row">
              <div class="contact-simple-label">
                <span class="contact-simple-icon">📱</span>
                <span>官方平台</span>
              </div>
              <div class="contact-simple-value">微信公众号、抖音号</div>
            </div>
            <div style="text-align: center; margin-top: 1rem;">
              <button @click="goToAI" class="ai-btn">💬 一键咨询</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import TabBar from '../components/TabBar.vue'
import { Clock, Star } from '@element-plus/icons-vue'
import { Grid, Money, Management, Document, Trophy, User, Reading } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/GesiXiehui')
}

const goToAI = () => {
  router.push('/card/GesiXiehui/ai-promoter')
}

const copyPhone = () => {
  navigator.clipboard.writeText('18827726669')
  alert('电话号码已复制')
}

const callPhone = () => {
  window.open('tel:18827726669')
}
</script>

<style scoped>
.page {
  min-height: 100vh;
  background: #f8fafc;
  padding-bottom: 80px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  flex: 1;
  text-align: center;
  padding-right: 3rem; /* 平衡左侧按钮 */
}

.content {
  padding-top: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  margin-bottom: 1.5rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-card {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  text-align: center;
  border-radius: 0.75rem;
  padding: 2rem 1.5rem;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
}

.welcome-card h1 {
  margin: 0 0 0.75rem 0;
  font-size: 1.6rem;
  font-weight: 600;
  line-height: 1.3;
}

.welcome-card p {
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 400;
}

.card h2 {
  color: #c41b21;
  margin: 0 0 1rem 0;
  text-align: center;
  font-size: 1.3rem;
  font-weight: 600;
}

.card video {
  display: block;
  margin: 0 auto;
}

.services {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.service {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
}

.service:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.service h3 {
  color: #c41b21;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.service p {
  color: #666;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.service ul {
  margin: 0;
  padding-left: 1rem;
}

.service li {
  color: #555;
  line-height: 1.5;
  margin-bottom: 0.3rem;
  font-size: 0.85rem;
}

.service li:last-child {
  margin-bottom: 0;
}

/* 入会指南紧凑布局 */
.membership-compact {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.membership-item {
  background: #fafafa;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #f0f0f0;
}

.membership-item h3 {
  color: #c41b21;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.membership-item p {
  color: #555;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.fees-compact {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  margin-top: 0.5rem;
}

.fee-row {
  background: #fef5f5;
  padding: 0.6rem 0.8rem;
  border-radius: 0.3rem;
  border: 1px solid #fed7d7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-label {
  color: #555;
  font-size: 0.9rem;
  font-weight: 500;
}

.fee-value {
  color: #c41b21;
  font-weight: 600;
  font-size: 0.9rem;
}

/* 联系我们简洁布局 */
.contact-simple {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
  margin-top: 0.5rem;
}

.contact-simple-row {
  background: #fef5f5;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #fed7d7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 2.5rem;
}

.contact-simple-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #c41b21;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 4rem;
}

.contact-simple-icon {
  font-size: 1rem;
}

.contact-simple-value {
  flex: 1;
  text-align: right;
  color: #555;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
}

.phone-number {
  font-weight: 500;
}

.contact-simple-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-simple {
  background: #c41b21;
  color: white;
  border: none;
  padding: 0.25rem 0.6rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-simple:hover {
  background: #a01419;
  transform: translateY(-1px);
}

.btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  margin-left: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn:hover {
  background: linear-gradient(135deg, #a01419, #c41b21);
  transform: translateY(-1px);
}

.ai-btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 2rem;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.ai-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(196, 27, 33, 0.4);
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #c41b21, #e53e3e);
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -0.375rem;
  top: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  background: #c41b21;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-item.highlight::before {
  background: #e53e3e;
  box-shadow: 0 0 0 4px rgba(196, 27, 33, 0.2);
}

.timeline-date {
  color: #c41b21;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.timeline-content {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .timeline {
    padding-left: 3rem;
  }
  .timeline::before {
    left: 1rem;
  }
  .timeline-item::before {
    left: 0.125rem;
  }
}


/* 响应式设计 */
@media (min-width: 768px) {
  .welcome-card h1 {
    font-size: 1.9rem;
  }

  .welcome-card p {
    font-size: 1.1rem;
  }

  .services {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .membership-compact {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .contact-simple-row {
    padding: 1rem 1.25rem;
  }

  .contact-simple-label {
    min-width: 5rem;
  }

  .fees-compact {
    gap: 0.6rem;
  }

  .section {
    margin-bottom: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .services {
    grid-template-columns: repeat(3, 1fr);
  }

  .welcome-card h1 {
    font-size: 2.1rem;
  }

  .welcome-card p {
    font-size: 1.2rem;
  }

  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* 最新活动样式 */
.activity-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.activity-item:last-child {
  margin-bottom: 0;
}

.activity-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.activity-item h3 {
  color: #c41b21;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.activity-intro {
  color: #555;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
  text-align: justify;
}

.activity-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #f5f5f5;
}

.activity-section:last-child {
  margin-bottom: 0;
}

.activity-section h4 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.activity-section h5 {
  color: #c41b21;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.activity-section p {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.6;
  margin: 0 0 1rem 0;
  text-align: justify;
}

.activity-image {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.activity-conclusion {
  background: #fef5f5;
  border: 1px solid #fed7d7;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #c41b21;
  font-weight: 500;
  margin: 1rem 0 0 0;
  text-align: center;
}

.stage-goals {
  margin-top: 1rem;
}

.stage-goals h4 {
  color: #c41b21;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
}

.goal-item {
  background: white;
  border: 1px solid #f5f5f5;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.goal-item:last-child {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .activity-item h3 {
    font-size: 1.4rem;
  }

  .activity-intro {
    font-size: 1.05rem;
  }

  .activity-section {
    padding: 1.25rem;
  }

  .activity-section h4 {
    font-size: 1.2rem;
  }

  .activity-section p {
    font-size: 1rem;
  }
}
</style>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 欢迎页样式 */
.welcome-section {
  margin-bottom: 2rem;
}

.welcome-card {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  color: white;
  box-shadow: 0 8px 30px rgba(196, 27, 33, 0.3);
}

.association-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.association-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* 通用卡片样式 */
.section-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  color: #c41b21;
  margin: 0 0 1.5rem 0;
  text-align: center;
  font-weight: 600;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #666;
  text-align: center;
  margin: 0 0 2rem 0;
}

/* 视频样式 */
.video-container {
  text-align: center;
}

.intro-video {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 关于我们样式 */
.about-text {
  font-size: 1rem;
  line-height: 1.8;
  color: #555;
  margin: 0;
  text-align: justify;
}

/* 六大服务中心网格布局 */
.service-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-top: 1rem;
}

@media (min-width: 600px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.service-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  min-height: 0;
  box-sizing: border-box;
}

.service-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 27, 33, 0.1);
  border-color: #c41b21;
}

.service-item img {
  width: 100%;
  max-width: 220px;
  height: auto;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.service-item h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.service-item .el-icon {
  font-size: 1.5rem;
  color: #c41b21;
}

/* 入会指南样式 */
.membership-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.membership-item h3 {
  font-size: 1.2rem;
  color: #c41b21;
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.membership-item p {
  font-size: 1rem;
  color: #555;
  margin: 0;
  line-height: 1.6;
}

.fee-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #fef5f5;
  border-radius: 0.5rem;
  border: 1px solid #fed7d7;
}

.fee-type {
  font-weight: 500;
  color: #333;
}

.fee-amount {
  font-weight: 600;
  color: #c41b21;
  font-size: 1.1rem;
}

/* 联系我们样式 */
.contact-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1.25rem;
  background: #fef5f5;
  border-radius: 0.75rem;
  border: 1px solid #fed7d7;
}

.contact-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #c41b21;
}

.contact-label .el-icon {
  font-size: 1.2rem;
}

.contact-value {
  color: #555;
  font-size: 1rem;
  line-height: 1.5;
}

.contact-value-with-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border: none;
  color: white;
}

.action-btn:hover {
  background: linear-gradient(135deg, #a01419, #c41b21);
}

.consultation-section {
  text-align: center;
  margin-top: 1rem;
}

.consultation-btn {
  background: linear-gradient(135deg, #c41b21, #e53e3e);
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 2rem;
  box-shadow: 0 4px 15px rgba(196, 27, 33, 0.3);
  transition: all 0.3s ease;
}

.consultation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(196, 27, 33, 0.4);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .association-title {
    font-size: 2.2rem;
  }

  .association-subtitle {
    font-size: 1.3rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .membership-content {
    flex-direction: row;
    gap: 3rem;
  }

  .membership-item {
    flex: 1;
  }

  .contact-item {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .contact-value-with-actions {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }

  .fee-list {
    gap: 1rem;
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .association-title {
    font-size: 2.5rem;
  }

  .association-subtitle {
    font-size: 1.4rem;
  }
}
</style>
