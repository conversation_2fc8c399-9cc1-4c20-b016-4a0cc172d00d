import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'wanWangKeJiHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片'
    }
  },
  {
    path: '/company-intro',
    name: 'wanWangKeJiCompanyIntro',
    component: () => import('./views/CompanyIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 企业介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'wanWangKeJiAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - AI宣传员'
    }
  },
  {
    path: '/case-center',
    name: 'wanWangKeJiCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 产品中心'
    }
  },
  {
    path: '/smart-law',
    name: 'wanWangKeJiSmartLaw',
    component: () => import('./views/SmartLawView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png',
      title: '江西万网科技AI名片 - 智慧法治'
    }
  }
]

export default routes
