import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'gesiXiehuiHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片'
    }
  },
  {
    path: '/association-intro',
    name: 'gesiXiehuiAssociationIntro',
    component: () => import('./views/AssociationIntroView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 协会介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'gesiXiehuiAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - AI宣传员'
    }
  },
  {
    path: '/association-news',
    name: 'gesiXiehuiAssociationNews',
    component: () => import('./views/AssociationNewsView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png',
      title: '个私协会AI名片 - 协会动态'
    }
  }
]

export default routes
