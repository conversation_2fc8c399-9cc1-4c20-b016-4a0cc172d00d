<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  School,
  Document,
  Trophy,
  TrendCharts,
  Star,
  Check,
  DataAnalysis,
  Files
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/LinTianKeJi')
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <button @click="goBack" class="back-btn">
        ‹ 返回
      </button>
      <h1>服务案例</h1>
    </div>

    <div class="content">
      <!-- 典型案例学校 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><School /></el-icon>
            服务案例学校
          </h2>

          <!-- 弋阳县试点学校 -->
          <div class="case-block">
            <h3>1. 弋阳县试点学校</h3>
            <div class="policy-support">
              <h4>政策支持</h4>
              <p>2023年4月被教育部列为全国38个信息技术支撑学生综合素质评价试点区域之一</p>
             
            </div>
            <div class="highlights">
              <h4>实践亮点</h4>
              <div class="highlight-list">
                <div class="highlight-item">
                  <el-icon><Check /></el-icon>
                  <div>
                    <strong>全面实施"伴随性"评价改革</strong>，要求教师全员参与（每日评价1-2条）
                    
                  </div>
                </div>
                <div class="highlight-item">
                  <el-icon><Check /></el-icon>
                  <div>家长每周上报"五项管理"数据</div>
                </div>
                <div class="highlight-item">
                  <el-icon><Check /></el-icon>
                  <div>建立学生电子成长档案，档案填写完整率达100%</div>
                </div>
                <div class="highlight-item">
                  <el-icon><Trophy /></el-icon>
                  <div><strong>2023年全国试点区域测评完成率排名第五</strong></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 广信区清水乡中心小学 -->
          <div class="case-block">
            <h3>2. 广信区清水乡中心小学</h3>
            <div class="policy-support">
              <h4>政策支持</h4>
              <p>落实《深化新时代教育评价改革总体方案》"三全育人"要求</p>
            </div>
            <div class="highlights">
              <h4>实践亮点</h4>
              <div class="highlight-list">
                <div class="highlight-item">
                  <el-icon><Star /></el-icon>
                  <div>创新"五有若水少年"评价体系（德智体美劳多维度）</div>
                </div>
                <div class="highlight-item">
                  <el-icon><TrendCharts /></el-icon>
                  <div>教师参与率98%，家长接入"微家校"平台率达98%</div>
                </div>
                <div class="highlight-item">
                  <el-icon><Trophy /></el-icon>
                  <div><strong>教学质量评估从全县第27位跃升至第6位（2022年）</strong></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 横峰县试点学校 -->
          <div class="case-block">
            <h3>3. 横峰县试点学校</h3>
            <div class="policy-support">
              <h4>政策支持</h4>
              <p>执行上饶市《推进新时代中小学生评价改革贯彻落实意见》</p>
            </div>
            <div class="highlights">
              <h4>实践亮点</h4>
              <div class="highlight-list">
                <div class="highlight-item">
                  <el-icon><DataAnalysis /></el-icon>
                  <div>教师评价数据每周通报</div>
                </div>
                <div class="highlight-item">
                  <el-icon><Trophy /></el-icon>
                  <div>考核得分90分以上评为"先进学校"</div>
                </div>
                <div class="highlight-item">
                  <el-icon><Files /></el-icon>
                  <div>研学活动与电子档案结合，家长上传活动心得及照片</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心政策支持 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Document /></el-icon>
            核心政策支持
          </h2>

          <!-- 国家级政策 -->
          <div class="policy-block">
            <h3>1. 国家级政策</h3>
            <div class="policy-list">
              <div class="policy-item">
                <div class="policy-title">《深化新时代教育评价改革总体方案》（2020年）</div>
                <div class="policy-desc">破除"五唯"评价</div>
              </div>
              <div class="policy-item">
                <div class="policy-title">教育部试点通知（2023年）</div>
                <div class="policy-desc">弋阳县列为全国试点</div>
                <div class="pdf-download">
                  <a href="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/JiaoYuBu.pdf" target="_blank" class="pdf-link">
                    <el-icon><Document /></el-icon>
                    查看政策文件
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- 省级/市级政策 -->
          <div class="policy-block">
            <h3>2. 省级/市级政策</h3>
            <div class="policy-list">
              <div class="policy-item">
                <div class="policy-title">上饶市评价改革意见（2022年）</div>
                <div class="policy-features">
                  <div class="feature">建立多维度评价体系</div>
                  <div class="feature">明确教师每日评价、家长每周上报要求</div>
                </div>
                <div class="pdf-download">
                  <a href="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/RaoShuZi.pdf" target="_blank" class="pdf-link">
                    <el-icon><Document /></el-icon>
                    查看政策文件
                  </a>
                </div>
              </div>
              <div class="policy-item">
                <div class="policy-title">弋阳县考核方案（2024年）</div>
                <div class="policy-features">
                  <div class="feature">平台使用数据纳入考核</div>
                  <div class="feature">LED屏公示排名</div>
                  <div class="feature">对考核低于60分学校约谈</div>
                </div>
                <div class="pdf-download">
                  <a href="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/YiYangZhongXiaoXueKaoHeFangAn.pdf" target="_blank" class="pdf-link">
                    <el-icon><Document /></el-icon>
                    查看政策文件
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- 县级落地政策 -->
          <div class="policy-block">
            <h3>3. 县级落地政策</h3>
            <div class="policy-list">
              <div class="policy-item">
                <div class="policy-title">横峰县考核方案（2023年）</div>
                <div class="policy-features">
                  <div class="feature">教师评价率、家长提交率要求100%</div>
                  <div class="feature">电子档案缺项扣分</div>
                  <div class="feature">考核结果与评优挂钩</div>
                </div>
                <div class="pdf-download">
                  <a href="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/HengFeng.pdf" target="_blank" class="pdf-link">
                    <el-icon><Document /></el-icon>
                    查看政策文件
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关键成效 -->
      <div class="section">
        <div class="card">
          <h2>
            <el-icon><Trophy /></el-icon>
            关键成效
          </h2>
          <div class="achievements">
            <div class="achievement-item">
              <div class="achievement-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="achievement-content">
                <h4>家校协同强化</h4>
                <p>家长通过平台参与"五项管理"，形成家校共育闭环</p>
              </div>
            </div>
            <div class="achievement-item">
              <div class="achievement-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="achievement-content">
                <h4>数据驱动管理</h4>
                <p>自动生成班级、寝室排名，提供评优依据</p>
              </div>
            </div>
            <div class="achievement-item">
              <div class="achievement-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="achievement-content">
                <h4>政策标杆效应</h4>
                <p>弋阳县成为全国改革样板</p>
              </div>
            </div>
          </div>

          <div class="note">
            <p><strong>注：</strong>以上案例与政策均来自官方文件，可直接用于产品推广或政策汇报。</p>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
}

.header {
  background: linear-gradient(135deg, #dcfdfa, #a7f3d0);
  color: #059669;
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  height: 3.5rem;
  box-sizing: border-box;
}

.back-btn {
  background: none;
  border: none;
  color: #059669;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.back-btn:hover {
  background-color: rgba(5, 150, 105, 0.1);
}

.header h1 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.content {
  padding-top: 4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 1.5rem;
}

.card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.card h2 {
  color: #059669;
  margin: 0 0 1.5rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card h3 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.card h4 {
  color: #059669;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.card p {
  color: #555;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

/* 案例块样式 */
.case-block {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.case-block:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.policy-support {
  background: #f0fdf4;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  border-left: 4px solid #10b981;
}

.highlights {
  margin-top: 1rem;
}

.highlight-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fffe;
  border-radius: 0.5rem;
  border-left: 4px solid #059669;
}

.highlight-item .el-icon {
  color: #059669;
  font-size: 1.1rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

/* 图片容器 */
.image-container {
  margin: 0.75rem 0;
}

.case-image {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.case-image:hover {
  transform: scale(1.02);
}

/* 政策块样式 */
.policy-block {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.policy-block:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.policy-list {
  margin-top: 1rem;
}

.policy-item {
  background: #f8fffe;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  border-left: 4px solid #10b981;
}

.policy-item:last-child {
  margin-bottom: 0;
}

.policy-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.policy-desc {
  color: #666;
  margin-bottom: 0.75rem;
}

.policy-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.feature {
  color: #555;
  padding-left: 1rem;
  position: relative;
}

.feature::before {
  content: '•';
  color: #059669;
  position: absolute;
  left: 0;
}

/* PDF下载按钮 */
.pdf-download {
  margin-top: 0.75rem;
}

.pdf-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #059669;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
}

.pdf-link:hover {
  background: #047857;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);
}

.pdf-link .el-icon {
  font-size: 1rem;
}

/* 成效展示 */
.achievements {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.achievement-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f0fdf4;
  border-radius: 0.75rem;
  border: 1px solid #dcfce7;
}

.achievement-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #059669, #10b981);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.achievement-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.achievement-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 注释样式 */
.note {
  background: #f8fffe;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #059669;
  margin-top: 1.5rem;
}

.note p {
  margin: 0;
  color: #555;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .highlight-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .achievements {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .case-image {
    max-width: 500px;
  }
}

@media (min-width: 1024px) {
  .highlight-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .achievements {
    grid-template-columns: repeat(3, 1fr);
  }

  .policy-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}
</style>
